import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Client } from './schemas/client.schema';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class RegisterClientService {
  constructor(@InjectModel(Client.name) private readonly clientModel: Model<Client>) {}

  async registerClient(applicationName: string, description?: string): Promise<Client> {
    const apiKey = uuidv4(); // Generate a unique API key
    const newClient = new this.clientModel({ apiKey, applicationName, description });
    return await newClient.save();
  }

  async updateClient(apiKey: string, updates: Partial<Client>): Promise<Client | null> {
    return await this.clientModel.findOneAndUpdate({ apiKey }, updates, { new: true });
  }

  async deactivateClient(apiKey: string): Promise<Client | null> {
    return await this.clientModel.findOneAndUpdate(
      { apiKey },
      { status: 'INACTIVE' },
      { new: true },
    );
  }
}
