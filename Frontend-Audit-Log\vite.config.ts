import path from 'path';
import react from '@vitejs/plugin-react';
import { defineConfig } from 'vite';

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  server: {
    host: true, // Enables external access
    hmr: {
      overlay: false, // Disables that red error overlay
    },
    allowedHosts: ['dev-auditlog.clinicaltrac.net'], // 👈 Add this line
  },
});
