{"version": 3, "file": "ouath.service.js", "sourceRoot": "", "sources": ["../../src/ouath/ouath.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA4C;AAC5C,+CAA+C;AAC/C,uCAAiC;AAI1B,IAAM,oBAAoB,GAA1B,MAAM,oBAAoB;IAC/B,YACiD,SAA+B;QAA/B,cAAS,GAAT,SAAS,CAAsB;IAC7E,CAAC;IAEJ,KAAK,CAAC,YAAY,CAAC,YAAoC;QACrD,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;QAClD,OAAO,QAAQ,CAAC,IAAI,EAAE,CAAC;IACzB,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,QAAgB;QACjC,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;IACvE,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,QAAgB;QACnC,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;IAC3E,CAAC;CAEF,CAAA;AAlBY,oDAAoB;+BAApB,oBAAoB;IADhC,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,sBAAW,EAAC,eAAe,CAAC,CAAA;qCAA6B,gBAAK;GAFtD,oBAAoB,CAkBhC"}