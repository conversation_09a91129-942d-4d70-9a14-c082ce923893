// import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON><PERSON>eader, } from "@/components/ui/dialog";
// import { But<PERSON> } from "@/components/ui/button";
import { CustomLoader } from "./customLoader";

// interface SessionExpiredModalProps {
//   open: boolean;
//   onClose: () => void;
// }

export function SessionExpiredModal() {
  // const handleOkClick = () => {
  //   onClose();
  //   setTimeout(() => {
  //     window.close(); // Try to close the tab
  //   }, 100); // Giving some time for the browser to register the action
  // };
  

  return (
    // <Dialog open={open} onOpenChange={onClose}>
    //   <DialogContent className="max-w-sm text-center">
    //     <DialogHeader>
    //       {/* <DialogTitle className="text-red-600 text-lg">Session Expired</DialogTitle> */}
    //     </DialogHeader>
    //     {/* <p className="text-gray-600">Your session has expired. Please log in again.</p> */}
    //     <DialogFooter>
    //       {/* <Button variant="destructive" onClick={handleOkClick}>OK</Button> */}
    //     </DialogFooter>
    //   </DialogContent>
    // </Dialog>

    <>
    <CustomLoader/>
    </>
  );
}
