{"version": 3, "file": "logs.service.js", "sourceRoot": "", "sources": ["../../../src/modules/logs-module/logs.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,+CAA+C;AAC/C,uCAAiC;AACjC,iEAAsD;AAEtD,yEAA8D;AAE9D,IAAY,OAGX;AAHD,WAAY,OAAO;IACjB,+CAAa,CAAA;IACb,uDAAiB,CAAA;AACnB,CAAC,EAHW,OAAO,uBAAP,OAAO,QAGlB;AAGM,IAAM,WAAW,GAAjB,MAAM,WAAW;IACtB,YAC+C,aAA8B,EAE1D,iBAAsC;QAFV,kBAAa,GAAb,aAAa,CAAiB;QAE1D,sBAAiB,GAAjB,iBAAiB,CAAqB;IACtD,CAAC;IAEJ,KAAK,CAAC,SAAS,CAAC,GAAsB,EAAE,UAAkB;QAGxD,MAAM,eAAe,GAAG;YACtB,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC;YAChD,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC;SACzD,CAAC;QAEF,MAAM,OAAO,GAAG,eAAe,CAAC,UAAU,CAAC,CAAC;QAE5C,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,wBAAwB,UAAU,EAAE,CAAC,CAAC;QACxD,CAAC;QAED,IAAI,CAAC;YACH,OAAO,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC;QAC5B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,4CAA4C,UAAU,KAAK,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YACzF,MAAM,KAAK,CAAC;QACd,CAAC;IAEH,CAAC;IAGO,WAAW,CAAC,GAAQ;QAC1B,IAAI,CAAC;YACH,eAAM,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAA;YACjC,MAAM,MAAM,GAAG,IAAI,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;YAC3C,OAAO,MAAM,CAAC,IAAI,EAAE,CAAC;QAEvB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,mCAAmC,CAAC,CAAA;QACnD,CAAC;IACH,CAAC;IAGO,eAAe,CAAC,GAAQ;QAC9B,IAAI,CAAC;YACH,eAAM,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAA;YACrC,MAAM,MAAM,GAAG,IAAI,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC;YAC/C,OAAO,MAAM,CAAC,IAAI,EAAE,CAAC;QAEvB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,mCAAmC,CAAC,CAAA;QACnD,CAAC;IACH,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,MAA0C;QACtD,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,CAAC;IACnD,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,QAAgB;QACtC,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;IACzD,CAAC;IAED,KAAK,CAAC,sBAAsB,CAC1B,OAA4B,EAC5B,IAAI,GAAG,CAAC,EACR,KAAK,GAAG,EAAE,EACV,IAAI,GAAG,YAAY,EACnB,UAAkB;QAOlB,MAAM,KAAK,GAAQ,EAAE,CAAC;QACtB,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAKjC,IAAI,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACvC,KAAK,CAAC,SAAS,GAAG,EAAE,CAAC;YACrB,IAAI,OAAO,CAAC,QAAQ,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC;gBAC7D,KAAK,CAAC,SAAS,CAAC,IAAI,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YACpD,CAAC;YACD,IAAI,OAAO,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC;gBACzD,KAAK,CAAC,SAAS,CAAC,IAAI,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YAClD,CAAC;QACH,CAAC;QAGD,IAAI,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,QAAQ,KAAK,EAAE,EAAE,CAAC;YAChD,KAAK,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;QACpC,CAAC;QAGD,KAAK,MAAM,GAAG,IAAI,OAAO,EAAE,CAAC;YAC1B,IAAI,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC;gBAAE,SAAS;YAEzE,IAAI,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBACtB,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,EAAE,QAAQ,EAAE,GAAG,EAAE,CAAC;YACvD,CAAC;iBAAM,CAAC;gBACN,KAAK,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;YAC5B,CAAC;QACH,CAAC;QAGD,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACnB,MAAM,WAAW,GAAG,EAAE,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,QAAQ,EAAE,GAAG,EAAE,CAAC;YAC9D,KAAK,CAAC,GAAG,GAAG;gBACV,EAAE,QAAQ,EAAE,WAAW,EAAE;gBACzB,EAAE,QAAQ,EAAE,WAAW,EAAE;gBACzB,EAAE,MAAM,EAAE,WAAW,EAAE;gBACvB,EAAE,QAAQ,EAAE,WAAW,EAAE;gBACzB,EAAE,OAAO,EAAE,WAAW,EAAE;gBAExB;oBACE,IAAI,EAAE;wBACJ,EAAE,UAAU,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE;wBAClD;4BACE,KAAK,EAAE;gCACL,GAAG,EAAE;oCACH;wCACE,KAAK,EAAE;4CACL,OAAO,EAAE;gDACP,KAAK,EAAE,EAAE,cAAc,EAAE,WAAW,EAAE;gDACtC,EAAE,EAAE,MAAM;gDACV,IAAI,EAAE;oDACJ,IAAI,EAAE;wDACJ,EAAE,GAAG,EAAE,CAAC,EAAE,KAAK,EAAE,UAAU,EAAE,EAAE,QAAQ,CAAC,EAAE;wDAC1C,EAAE,WAAW,EAAE,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,OAAO,CAAC,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,EAAE;qDAC5E;iDACF;6CACF;yCACF;qCACF;oCACD,CAAC;iCACF;6BACF;yBACF;qBACF;iBACF;aACF,CAAC;QACJ,CAAC;QAKD,MAAM,UAAU,GAA8B,EAAE,SAAS,EAAE,CAAC,CAAc,EAAE,CAAC;QAI7E,MAAM,eAAe,GAAG;YACtB,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC;YACrD,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC;SAC7D,CAAC;QAGF,MAAM,OAAO,GAAG,eAAe,CAAC,UAAU,CAAC,CAAC;QAE5C,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,wBAAwB,UAAU,EAAE,CAAC,CAAC;QACxD,CAAC;QAED,IAAI,CAAC;YAEH,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;YAE3D,OAAO;gBACL,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,iBAAiB,EAAE,IAAI,GAAG,KAAK,GAAG,IAAI,CAAC,UAAU;gBACjD,qBAAqB,EAAE,IAAI,GAAG,CAAC;aAChC,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,6CAA6C,UAAU,KAAK,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAC1F,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,KAAU,EAAE,IAAY,EAAE,KAAa,EAAE,UAAqC;QAC3G,IAAI,CAAC;YACH,eAAM,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAA;YACjC,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YAElE,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,aAAa;iBACpC,IAAI,CAAC,KAAK,CAAC;iBACX,IAAI,CAAC,IAAI,CAAC;iBACV,KAAK,CAAC,KAAK,CAAC;iBACZ,IAAI,CAAC,UAAU,CAAC;iBAChB,IAAI,EAAE,CAAC;YAER,eAAM,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAA;YAC1C,OAAO,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC;QAC9B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,oCAAoC,CAAC,CAAA;QACpD,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,KAAU,EAAE,IAAY,EAAE,KAAa,EAAE,UAAqC;QAC9G,IAAI,CAAC;YAEH,eAAM,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAA;YAEjC,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YAEtE,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,iBAAiB;iBACzC,IAAI,CAAC,KAAK,CAAC;iBACX,IAAI,CAAC,IAAI,CAAC;iBACV,KAAK,CAAC,KAAK,CAAC;iBACZ,IAAI,CAAC,UAAU,CAAC;iBAChB,IAAI,EAAE,CAAC;YAEP,eAAM,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAA;YAC1C,OAAO,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC;QAC9B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,oCAAoC,CAAC,CAAA;QACpD,CAAC;IACH,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,UAAkB;QACtC,MAAM,eAAe,GAAG;YACtB,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,aAAa;YAC7C,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,iBAAiB;SACtD,CAAC;QAEF,MAAM,OAAO,GAAG,eAAe,CAAC,UAAU,CAAC,CAAC;QAC5C,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;QACzC,CAAC;QAED,MAAM,KAAK,GAAG,OAAO,EAAE,CAAC;QACxB,MAAM,YAAY,GAAG,MAAM,KAAK,CAAC,SAAS,CAAC;YACzC;gBACE,MAAM,EAAE;oBACN,IAAI,EAAE;wBACJ,EAAE,QAAQ,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE;wBAC3B,EAAE,QAAQ,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE;wBACzB,EAAE,UAAU,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE;wBAC7B,EAAE,UAAU,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE;wBAC3B;4BACE,KAAK,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE,WAAW,EAAE,EAAE,CAAC,CAAC,EAAE;yBAC7C;qBACF;iBACF;aACF;YACD;gBACE,MAAM,EAAE;oBACN,GAAG,EAAE,EAAE,QAAQ,EAAE,WAAW,EAAE,UAAU,EAAE,aAAa,EAAE;iBAC1D;aACF;YACD;gBACE,QAAQ,EAAE;oBACR,GAAG,EAAE,CAAC;oBACN,QAAQ,EAAE,eAAe;oBACzB,UAAU,EAAE,iBAAiB;iBAC9B;aACF;SACF,CAAC,CAAC;QAEH,OAAO,YAAY,CAAC;IACtB,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,UAAkB;QACtC,MAAM,eAAe,GAAG;YACtB,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,aAAa;YAC7C,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,iBAAiB;SACtD,CAAC;QAEF,MAAM,OAAO,GAAG,eAAe,CAAC,UAAU,CAAC,CAAC;QAC5C,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;QACzC,CAAC;QAED,MAAM,KAAK,GAAG,OAAO,EAAE,CAAC;QACxB,MAAM,YAAY,GAAG,MAAM,KAAK,CAAC,SAAS,CAAC;YACzC;gBACE,MAAM,EAAE;oBACN,GAAG,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE;iBAC3B;aACF;YACD;gBACE,QAAQ,EAAE;oBACR,GAAG,EAAE,CAAC;oBACN,MAAM,EAAE,aAAa;iBACtB;aACF;SACF,CAAC,CAAC;QACH,OAAO,YAAY,CAAC;IACtB,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,UAAkB;QACxC,MAAM,eAAe,GAAG;YACtB,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,aAAa;YAC7C,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,iBAAiB;SACtD,CAAC;QAEF,MAAM,OAAO,GAAG,eAAe,CAAC,UAAU,CAAC,CAAC;QAC5C,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;QACzC,CAAC;QAED,MAAM,KAAK,GAAG,OAAO,EAAE,CAAC;QACxB,MAAM,cAAc,GAAG,MAAM,KAAK,CAAC,SAAS,CAAC;YAC3C;gBACE,MAAM,EAAE;oBACN,GAAG,EAAE,EAAE,QAAQ,EAAE,WAAW,EAAE;iBAC/B;aACF;YACD;gBACE,QAAQ,EAAE;oBACR,GAAG,EAAE,CAAC;oBACN,QAAQ,EAAE,eAAe;iBAC1B;aACF;SACF,CAAC,CAAC;QACH,OAAO,cAAc,CAAC;IACxB,CAAC;IAEH,KAAK,CAAC,aAAa,CAAC,UAAkB,EAAE,QAAiB;QACrD,MAAM,eAAe,GAAG;YACtB,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,aAAa;YAC7C,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,iBAAiB;SACtD,CAAC;QAEF,MAAM,OAAO,GAAG,eAAe,CAAC,UAAU,CAAC,CAAC;QAC5C,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;QACzC,CAAC;QAED,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,OAAO,EAAE,CAAC;YACxB,MAAM,eAAe,GAAU;gBAC7B,EAAE,QAAQ,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE;gBAC/B,EAAE,QAAQ,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE;gBAC3B,EAAE,QAAQ,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE;aAC1B,CAAC;YAEF,IAAI,QAAQ,EAAE,CAAC;gBACb,eAAe,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC,CAAC;YAC/C,CAAC;YAED,MAAM,UAAU,GAAG,MAAM,KAAK,CAAC,SAAS,CAAC;gBACvC;oBACE,MAAM,EAAE;wBACN,IAAI,EAAE,eAAe;qBACtB;iBACF;gBACD;oBACE,MAAM,EAAE;wBACN,GAAG,EAAE,SAAS;wBACd,QAAQ,EAAE,EAAE,MAAM,EAAE,WAAW,EAAE;wBACjC,QAAQ,EAAE,EAAE,MAAM,EAAE,WAAW,EAAE;qBAClC;iBACF;gBACD;oBACE,QAAQ,EAAE;wBACR,GAAG,EAAE,CAAC;wBACN,MAAM,EAAE,MAAM;wBACd,QAAQ,EAAE,CAAC;wBACX,QAAQ,EAAE,CAAC;qBACZ;iBACF;gBACD;oBACE,KAAK,EAAE;wBACL,MAAM,EAAE,CAAC;qBACV;iBACF;aACF,CAAC,CAAC;YAEH,OAAO,UAAU,CAAC;QACpB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,6CAA6C,UAAU,KAAK,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAC1F,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CACF,CAAA;AAxXY,kCAAW;sBAAX,WAAW;IADvB,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,sBAAW,EAAC,2BAAQ,CAAC,IAAI,CAAC,CAAA;IAC1B,WAAA,IAAA,sBAAW,EAAC,mCAAY,CAAC,IAAI,EAAE,qBAAqB,CAAC,CAAA;qCADM,gBAAK;QAE7B,gBAAK;GAJhC,WAAW,CAwXvB"}