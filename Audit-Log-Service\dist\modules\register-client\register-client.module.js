"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RegisterClientModule = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const register_client_controller_1 = require("./register-client.controller");
const register_client_service_1 = require("./register-client.service");
const client_schema_1 = require("./schemas/client.schema");
let RegisterClientModule = class RegisterClientModule {
};
exports.RegisterClientModule = RegisterClientModule;
exports.RegisterClientModule = RegisterClientModule = __decorate([
    (0, common_1.Module)({
        imports: [mongoose_1.MongooseModule.forFeature([{ name: client_schema_1.Client.name, schema: client_schema_1.ClientSchema }])],
        controllers: [register_client_controller_1.RegisterClientController],
        providers: [register_client_service_1.RegisterClientService],
    })
], RegisterClientModule);
//# sourceMappingURL=register-client.module.js.map