import { Schema as MongooseSchema, Document } from 'mongoose';
export declare class Authorization extends Document {
    authCode: string;
    school_name: string;
    school_id: string;
    state: string;
    isExpired: boolean;
    ipAddress: string;
}
export declare const AuthorizationSchema: MongooseSchema<Authorization, import("mongoose").Model<Authorization, any, any, any, Document<unknown, any, Authorization, any> & Authorization & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Authorization, Document<unknown, {}, import("mongoose").FlatRecord<Authorization>, {}> & import("mongoose").FlatRecord<Authorization> & Required<{
    _id: unknown;
}> & {
    __v: number;
}>;
