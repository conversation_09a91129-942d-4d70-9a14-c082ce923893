import { BackupService } from './backup.service';
export declare class BackupController {
    private readonly backupService;
    constructor(backupService: BackupService);
    createBackup(): Promise<{
        message: string;
    }>;
    createBackupWithUpload(): Promise<{
        statusCode: number;
        message: string;
        url?: string;
    }>;
    cleanupOldBackups(): Promise<{
        statusCode: number;
        message: string;
    }>;
}
