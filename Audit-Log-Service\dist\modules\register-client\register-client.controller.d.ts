import { RegisterClientService } from './register-client.service';
import { Client } from './schemas/client.schema';
export declare class RegisterClientController {
    private readonly registerClientService;
    constructor(registerClientService: RegisterClientService);
    registerClient(applicationName: string, description?: string): Promise<Client>;
    updateClient(apiKey: string, updates: Partial<Client>): Promise<Client | null>;
    deactivateClient(apiKey: string): Promise<Client | null>;
}
