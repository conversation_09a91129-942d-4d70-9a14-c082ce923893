"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LogSchema = void 0;
const mongoose_1 = require("mongoose");
exports.LogSchema = new mongoose_1.Schema({
    userId: { type: String, required: true },
    action: { type: String, enum: ['CREATE', 'READ', 'UPDATE', 'DELETE'], required: true },
    resource: { type: String, required: true },
    metadata: { type: mongoose_1.Schema.Types.Mixed },
}, {
    strict: false,
    timestamps: true,
});
//# sourceMappingURL=log.schema.js.map