import { HttpStatus } from '@nestjs/common';
import { LogsService } from './logs.service';
import { Log } from './schemas/log.schema';
export declare class LogsController {
    private readonly logsService;
    constructor(logsService: LogsService);
    createLog(log: Partial<Log>, req: Request): Promise<{
        statusCode: HttpStatus;
        message: string;
        data?: undefined;
        error?: undefined;
    } | {
        statusCode: HttpStatus;
        message: string;
        data: import("./schemas/audit-log.schema").AuditLog;
        error?: undefined;
    } | {
        statusCode: HttpStatus;
        message: string;
        error: any;
        data?: undefined;
    }>;
    getLogs(query: Record<string, string | undefined>): Promise<{
        statusCode: HttpStatus;
        message: string;
        data: import("./schemas/audit-log.schema").AuditLog;
        error?: undefined;
    } | {
        statusCode: HttpStatus;
        message: string;
        error: any;
        data?: undefined;
    }>;
    getLogsByResource(resource: string): Promise<{
        statusCode: HttpStatus;
        message: string;
        data: import("./schemas/audit-log.schema").AuditLog;
        error?: undefined;
    } | {
        statusCode: HttpStatus;
        message: string;
        error: any;
        data?: undefined;
    }>;
    searchWithDynamicFilters(filters: string, skip: string, limit: string, sort: string, search: string, service: string, userId?: string): Promise<{
        statusCode: HttpStatus;
        message: string;
        data: {
            data: import("./schemas/audit-log.schema").AuditLog[];
            totalCount: number;
            nextPageAvailable: boolean;
            previousPageAvailable: boolean;
        };
        error?: undefined;
    } | {
        statusCode: HttpStatus;
        message: string;
        error: any;
        data?: undefined;
    }>;
    getFilters(serviceKey: string, schoolId?: string): Promise<{
        statusCode: HttpStatus;
        message: string;
        filters: {
            schoolFilter: any;
            actionFilter: any;
            resourceFilter: any;
            userFilter: any[];
        };
        error?: undefined;
    } | {
        statusCode: HttpStatus;
        message: string;
        error: any;
        filters?: undefined;
    }>;
}
