"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BackupController = void 0;
const common_1 = require("@nestjs/common");
const backup_service_1 = require("./backup.service");
let BackupController = class BackupController {
    constructor(backupService) {
        this.backupService = backupService;
    }
    async createBackup() {
        const backupPath = await this.backupService.createBackup();
        return { message: `Backup created at ${backupPath}` };
    }
    async createBackupWithUpload() {
        try {
            const backupPath = await this.backupService.createBackup();
            const zipFilePath = await this.backupService.createZipFromBackup(backupPath);
            try {
                const uploadUrl = await this.backupService.uploadToGCP(zipFilePath);
                return {
                    statusCode: common_1.HttpStatus.OK,
                    message: 'Backup created, zipped, and uploaded successfully',
                    url: uploadUrl
                };
            }
            catch (uploadError) {
                return {
                    statusCode: common_1.HttpStatus.PARTIAL_CONTENT,
                    message: `Backup created and zipped successfully, but upload failed: ${uploadError.message}`
                };
            }
        }
        catch (error) {
            return {
                statusCode: common_1.HttpStatus.INTERNAL_SERVER_ERROR,
                message: `Backup with upload failed: ${error.message}`
            };
        }
    }
    async cleanupOldBackups() {
        try {
            await this.backupService.cleanupOldBackups();
            return {
                statusCode: common_1.HttpStatus.OK,
                message: 'Old backups cleaned up successfully'
            };
        }
        catch (error) {
            return {
                statusCode: common_1.HttpStatus.INTERNAL_SERVER_ERROR,
                message: `Failed to clean up old backups: ${error.message}`
            };
        }
    }
};
exports.BackupController = BackupController;
__decorate([
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], BackupController.prototype, "createBackup", null);
__decorate([
    (0, common_1.Post)('with-upload'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], BackupController.prototype, "createBackupWithUpload", null);
__decorate([
    (0, common_1.Post)('cleanup'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], BackupController.prototype, "cleanupOldBackups", null);
exports.BackupController = BackupController = __decorate([
    (0, common_1.Controller)('backup'),
    __metadata("design:paramtypes", [backup_service_1.BackupService])
], BackupController);
//# sourceMappingURL=backup.controller.js.map