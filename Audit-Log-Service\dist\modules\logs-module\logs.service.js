"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LogsService = exports.SERVICE = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const audit_log_schema_1 = require("./schemas/audit-log.schema");
const radiology_log_schema_1 = require("./schemas/radiology-log.schema");
var SERVICE;
(function (SERVICE) {
    SERVICE[SERVICE["AUDIT_LOG"] = 1] = "AUDIT_LOG";
    SERVICE[SERVICE["RADIOLOGY_LOG"] = 2] = "RADIOLOGY_LOG";
})(SERVICE || (exports.SERVICE = SERVICE = {}));
let LogsService = class LogsService {
    constructor(auditLogModel, radiologyLogModel) {
        this.auditLogModel = auditLogModel;
        this.radiologyLogModel = radiologyLogModel;
    }
    async createLog(log, serviceKey) {
        const serviceHandlers = {
            [SERVICE.AUDIT_LOG]: this.auditlogsDb.bind(this),
            [SERVICE.RADIOLOGY_LOG]: this.radiologylogsDb.bind(this),
        };
        const handler = serviceHandlers[serviceKey];
        if (!handler) {
            throw new Error(`Invalid service key: ${serviceKey}`);
        }
        try {
            return await handler(log);
        }
        catch (error) {
            common_1.Logger.error(`Error while inserting log for serviceKey ${serviceKey}: ${error.message}`);
            throw error;
        }
    }
    auditlogsDb(log) {
        try {
            common_1.Logger.log('Inserting AuditLogs');
            const newLog = new this.auditLogModel(log);
            return newLog.save();
        }
        catch (error) {
            common_1.Logger.error("Error While Inserting AuditlogsDb");
        }
    }
    radiologylogsDb(log) {
        try {
            common_1.Logger.log('Inserting RadioLogy Log');
            const newLog = new this.radiologyLogModel(log);
            return newLog.save();
        }
        catch (error) {
            common_1.Logger.error("Error While Inserting RadioLogyDB");
        }
    }
    async getLogs(filter) {
        return this.auditLogModel.findOne(filter).exec();
    }
    async getLogsByResource(resource) {
        return this.auditLogModel.findOne({ resource }).exec();
    }
    async findWithDynamicFilters(filters, skip = 0, limit = 10, sort = '-createdAt', serviceKey) {
        const query = {};
        console.log("Filters:", filters);
        if (filters.fromDate || filters.toDate) {
            query.createdAt = {};
            if (filters.fromDate && !isNaN(Date.parse(filters.fromDate))) {
                query.createdAt.$gte = new Date(filters.fromDate);
            }
            if (filters.toDate && !isNaN(Date.parse(filters.toDate))) {
                query.createdAt.$lte = new Date(filters.toDate);
            }
        }
        if (filters.isMobile && filters.isMobile !== "") {
            query.isMobile = filters.isMobile;
        }
        for (const key in filters) {
            if (['fromDate', 'toDate', 'search', 'isMobile'].includes(key))
                continue;
            if (key.includes('.')) {
                query[key] = { $regex: filters[key], $options: 'i' };
            }
            else {
                query[key] = filters[key];
            }
        }
        if (filters.search) {
            const searchRegex = { $regex: filters.search, $options: 'i' };
            query.$or = [
                { userName: searchRegex },
                { userType: searchRegex },
                { action: searchRegex },
                { resource: searchRegex },
                { message: searchRegex },
                {
                    $and: [
                        { 'metadata': { $exists: true, $type: 'object' } },
                        {
                            $expr: {
                                $gt: [
                                    {
                                        $size: {
                                            $filter: {
                                                input: { $objectToArray: '$metadata' },
                                                as: 'item',
                                                cond: {
                                                    $and: [
                                                        { $eq: [{ $type: '$$item.v' }, 'string'] },
                                                        { $regexMatch: { input: '$$item.v', regex: filters.search, options: 'i' } }
                                                    ]
                                                }
                                            }
                                        }
                                    },
                                    0
                                ]
                            }
                        }
                    ]
                },
            ];
        }
        const sortOption = { createdAt: -1 };
        const serviceHandlers = {
            [SERVICE.AUDIT_LOG]: this.audiltLogsSearch.bind(this),
            [SERVICE.RADIOLOGY_LOG]: this.radiologyLogsSearch.bind(this),
        };
        const handler = serviceHandlers[serviceKey];
        if (!handler) {
            throw new Error(`Invalid service key: ${serviceKey}`);
        }
        try {
            const data = await handler(query, skip, limit, sortOption);
            return {
                data: data.data,
                totalCount: data.totalCount,
                nextPageAvailable: skip + limit < data.totalCount,
                previousPageAvailable: skip > 0,
            };
        }
        catch (error) {
            common_1.Logger.error(`Error while searching logs for serviceKey ${serviceKey}: ${error.message}`);
            throw error;
        }
    }
    async audiltLogsSearch(query, skip, limit, sortOption) {
        try {
            common_1.Logger.log("searching auditLogs");
            const totalCount = await this.auditLogModel.countDocuments(query);
            const data = await this.auditLogModel
                .find(query)
                .skip(skip)
                .limit(limit)
                .sort(sortOption)
                .exec();
            common_1.Logger.log("Search Complete on auditLogs");
            return { data, totalCount };
        }
        catch (error) {
            common_1.Logger.error("error while searching in auditLogs");
        }
    }
    async radiologyLogsSearch(query, skip, limit, sortOption) {
        try {
            common_1.Logger.log("searching radiology");
            const totalCount = await this.radiologyLogModel.countDocuments(query);
            const data = await this.radiologyLogModel
                .find(query)
                .skip(skip)
                .limit(limit)
                .sort(sortOption)
                .exec();
            common_1.Logger.log("Search Complete on radiology");
            return { data, totalCount };
        }
        catch (error) {
            common_1.Logger.error("error while searching in radiology");
        }
    }
    async getSchoolFilter(serviceKey) {
        const serviceHandlers = {
            [SERVICE.AUDIT_LOG]: () => this.auditLogModel,
            [SERVICE.RADIOLOGY_LOG]: () => this.radiologyLogModel,
        };
        const handler = serviceHandlers[serviceKey];
        if (!handler) {
            throw new Error('Invalid service key');
        }
        const model = handler();
        const schoolFilter = await model.aggregate([
            {
                $match: {
                    $and: [
                        { schoolId: { $ne: null } },
                        { schoolId: { $ne: "" } },
                        { schoolName: { $ne: null } },
                        { schoolName: { $ne: "" } },
                        {
                            $expr: { $ne: [{ $toInt: "$schoolId" }, 0] }
                        }
                    ]
                }
            },
            {
                $group: {
                    _id: { schoolId: "$schoolId", schoolName: "$schoolName" },
                },
            },
            {
                $project: {
                    _id: 0,
                    schoolId: "$_id.schoolId",
                    schoolName: "$_id.schoolName",
                },
            },
        ]);
        return schoolFilter;
    }
    async getActionFilter(serviceKey) {
        const serviceHandlers = {
            [SERVICE.AUDIT_LOG]: () => this.auditLogModel,
            [SERVICE.RADIOLOGY_LOG]: () => this.radiologyLogModel,
        };
        const handler = serviceHandlers[serviceKey];
        if (!handler) {
            throw new Error('Invalid service key');
        }
        const model = handler();
        const actionFilter = await model.aggregate([
            {
                $group: {
                    _id: { action: '$action' },
                },
            },
            {
                $project: {
                    _id: 0,
                    action: '$_id.action',
                },
            },
        ]);
        return actionFilter;
    }
    async getResourceFilter(serviceKey) {
        const serviceHandlers = {
            [SERVICE.AUDIT_LOG]: () => this.auditLogModel,
            [SERVICE.RADIOLOGY_LOG]: () => this.radiologyLogModel,
        };
        const handler = serviceHandlers[serviceKey];
        if (!handler) {
            throw new Error('Invalid service key');
        }
        const model = handler();
        const resourceFilter = await model.aggregate([
            {
                $group: {
                    _id: { resource: '$resource' },
                },
            },
            {
                $project: {
                    _id: 0,
                    resource: '$_id.resource',
                },
            },
        ]);
        return resourceFilter;
    }
    async getUserFilter(serviceKey, schoolId) {
        const serviceHandlers = {
            [SERVICE.AUDIT_LOG]: () => this.auditLogModel,
            [SERVICE.RADIOLOGY_LOG]: () => this.radiologyLogModel,
        };
        const handler = serviceHandlers[serviceKey];
        if (!handler) {
            throw new Error('Invalid service key');
        }
        try {
            const model = handler();
            const matchConditions = [
                { userName: { $exists: true } },
                { userName: { $ne: null } },
                { userName: { $ne: "" } }
            ];
            if (schoolId) {
                matchConditions.push({ schoolId: schoolId });
            }
            const userFilter = await model.aggregate([
                {
                    $match: {
                        $and: matchConditions
                    }
                },
                {
                    $group: {
                        _id: '$userId',
                        userName: { $first: '$userName' },
                        userType: { $first: '$userType' }
                    },
                },
                {
                    $project: {
                        _id: 0,
                        userId: '$_id',
                        userName: 1,
                        userType: 1
                    },
                },
                {
                    $sort: {
                        userId: 1
                    }
                }
            ]);
            return userFilter;
        }
        catch (error) {
            common_1.Logger.error(`Error fetching user filter for serviceKey ${serviceKey}: ${error.message}`);
            throw error;
        }
    }
};
exports.LogsService = LogsService;
exports.LogsService = LogsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(audit_log_schema_1.AuditLog.name)),
    __param(1, (0, mongoose_1.InjectModel)(radiology_log_schema_1.RadiologyLog.name, 'radiologyConnection')),
    __metadata("design:paramtypes", [mongoose_2.Model,
        mongoose_2.Model])
], LogsService);
//# sourceMappingURL=logs.service.js.map