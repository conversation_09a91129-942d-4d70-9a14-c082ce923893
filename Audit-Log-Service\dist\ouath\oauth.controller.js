"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OauthController = void 0;
const common_1 = require("@nestjs/common");
const ouath_service_1 = require("./ouath.service");
const jwt_1 = require("@nestjs/jwt");
const logs_service_1 = require("../modules/logs-module/logs.service");
const skipauth_decorators_1 = require("../jwt_guard/decorators/skipauth.decorators");
let OauthController = class OauthController {
    constructor(authService, jwtService, logsService) {
        this.authService = authService;
        this.jwtService = jwtService;
        this.logsService = logsService;
    }
    async handleAuthorize(query, req, res) {
        const { state, school_id, redirect_uri } = query;
        const authCode = Math.random().toString(36).substring(2);
        const userIp = req.ip;
        await this.authService.saveAuthCode({
            authCode,
            school_id,
            state,
            ipAddress: userIp,
            isExpired: false
        });
        const redirectUrl = `${redirect_uri}?authorization_code=${authCode}&state=${state}&school_id=${school_id}`;
        return res.status(common_1.HttpStatus.OK).json({
            statusCode: common_1.HttpStatus.OK,
            message: 'Success',
            data: redirectUrl,
        });
    }
    async handleToken(query, res) {
        const { authorization_code, state, school_id } = query;
        console.log("query", { authorization_code, state, school_id });
        const authCode = await this.authService.findAuthCode(authorization_code);
        console.log("authCode", authCode);
        if (!authCode || authCode.state !== state || authCode.isExpired) {
            return res.status(common_1.HttpStatus.BAD_REQUEST).json({
                statusCode: common_1.HttpStatus.BAD_REQUEST,
                message: 'Invalid or expired authorization code',
            });
        }
        await this.authService.expireAuthCode(authorization_code);
        const payload = { school_id };
        const accessToken = this.jwtService.sign(payload, {
            secret: process.env.JWT_ACCESS_SECRET,
            expiresIn: '1d',
        });
        const refreshToken = this.jwtService.sign(payload, {
            secret: process.env.JWT_REFRESH_SECRET,
            expiresIn: '7d',
        });
        return res.status(common_1.HttpStatus.OK).json({
            statusCode: common_1.HttpStatus.OK,
            message: 'User Logged In successfully',
            data: {
                accessToken,
                refreshToken,
                school: {
                    school_id: school_id,
                },
            },
        });
    }
};
exports.OauthController = OauthController;
__decorate([
    (0, skipauth_decorators_1.SkipAuth)(),
    (0, common_1.Get)('/authorize'),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Req)()),
    __param(2, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object, Object]),
    __metadata("design:returntype", Promise)
], OauthController.prototype, "handleAuthorize", null);
__decorate([
    (0, skipauth_decorators_1.SkipAuth)(),
    (0, common_1.Get)('/token'),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], OauthController.prototype, "handleToken", null);
exports.OauthController = OauthController = __decorate([
    (0, common_1.Controller)('oauth'),
    __metadata("design:paramtypes", [ouath_service_1.AuthorizationService,
        jwt_1.JwtService,
        logs_service_1.LogsService])
], OauthController);
//# sourceMappingURL=oauth.controller.js.map