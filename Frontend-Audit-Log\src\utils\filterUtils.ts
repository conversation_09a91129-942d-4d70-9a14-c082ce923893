import { AuditLogType } from '@/types/audit';

export function filterLogsBySearch(logs: AuditLogType[], searchTerm: string): AuditLogType[] {
  if (!searchTerm) return logs;
  
  const term = searchTerm.toLowerCase();
  return logs.filter(log => {
    const message = log.message?.toLowerCase() ?? '';
    const userName = log.userName?.toLowerCase() ?? '';
    return message.includes(term) || userName.includes(term);
  });
}

export function filterLogsByResource(logs: AuditLogType[], resource: string): AuditLogType[] {
  if (resource === 'all') return logs;
  return logs.filter(log => log.resource === resource);
}

export function filterLogsByAction(logs: AuditLogType[], action: string): AuditLogType[] {
  if (action === 'all') return logs;
  return logs.filter(log => log.action === action);
}

export function filterLogsByDateRange(logs: AuditLogType[], dateRange: { from?: Date; to?: Date }): AuditLogType[] {
  if (!dateRange.from) return logs;
  
  return logs.filter(log => {
    const logDate = new Date(log.createdAt);
    if (isNaN(logDate.getTime())) return false;
    
    const afterStart = logDate >= dateRange.from!;
    const beforeEnd = !dateRange.to || logDate <= dateRange.to;
    return afterStart && beforeEnd;
  });
}