import { Model } from 'mongoose';
import { AuditLog } from './schemas/audit-log.schema';
import { RadiologyLog } from './schemas/radiology-log.schema';
export declare enum SERVICE {
    AUDIT_LOG = 1,
    RADIOLOGY_LOG = 2
}
export declare class LogsService {
    private readonly auditLogModel;
    private readonly radiologyLogModel;
    constructor(auditLogModel: Model<AuditLog>, radiologyLogModel: Model<RadiologyLog>);
    createLog(log: Partial<AuditLog>, serviceKey: number): Promise<AuditLog>;
    private auditlogsDb;
    private radiologylogsDb;
    getLogs(filter: Record<string, string | undefined>): Promise<AuditLog>;
    getLogsByResource(resource: string): Promise<AuditLog>;
    findWithDynamicFilters(filters: Record<string, any>, skip: number, limit: number, sort: string, serviceKey: number): Promise<{
        data: AuditLog[];
        totalCount: number;
        nextPageAvailable: boolean;
        previousPageAvailable: boolean;
    }>;
    private audiltLogsSearch;
    private radiologyLogsSearch;
    getSchoolFilter(serviceKey: string): Promise<any>;
    getActionFilter(serviceKey: string): Promise<any>;
    getResourceFilter(serviceKey: string): Promise<any>;
    getUserFilter(serviceKey: string, schoolId?: string): Promise<any[]>;
}
