{"root": ["./src/app.tsx", "./src/main.tsx", "./src/vite-env.d.ts", "./src/components/audit/actionfilter.tsx", "./src/components/audit/auditlogdetailssheet.tsx", "./src/components/audit/auditlogtable.tsx", "./src/components/audit/daterangefilter.tsx", "./src/components/audit/devicetypefilter.tsx", "./src/components/audit/resourcefilter.tsx", "./src/components/audit/schoolfilter.tsx", "./src/components/audit/sessionexpiredmodal.tsx", "./src/components/audit/userfilter.tsx", "./src/components/audit/customloader.tsx", "./src/components/ui/alert.tsx", "./src/components/ui/badge.tsx", "./src/components/ui/button.tsx", "./src/components/ui/calendar.tsx", "./src/components/ui/checkbox.tsx", "./src/components/ui/dialog.tsx", "./src/components/ui/form.tsx", "./src/components/ui/input.tsx", "./src/components/ui/label.tsx", "./src/components/ui/popover.tsx", "./src/components/ui/scroll-area.tsx", "./src/components/ui/select.tsx", "./src/components/ui/separator.tsx", "./src/components/ui/sheet.tsx", "./src/components/ui/skeleton.tsx", "./src/components/ui/sonner.tsx", "./src/components/ui/switch.tsx", "./src/components/ui/table.tsx", "./src/components/ui/tabs.tsx", "./src/components/ui/toast.tsx", "./src/components/ui/toaster.tsx", "./src/context/authprovider.tsx", "./src/controller/oauthcontroller.tsx", "./src/hooks/use-toast.ts", "./src/hooks/useauditlogfilters.ts", "./src/hooks/useauditlogs.ts", "./src/lib/utils.ts", "./src/services/api.ts", "./src/types/audit.ts", "./src/utils/dateutils.ts", "./src/utils/filterutils.ts"], "errors": true, "version": "5.7.3"}