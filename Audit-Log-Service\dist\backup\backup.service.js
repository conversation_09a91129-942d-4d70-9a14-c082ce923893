"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var BackupService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.BackupService = void 0;
const common_1 = require("@nestjs/common");
const schedule_1 = require("@nestjs/schedule");
const child_process_1 = require("child_process");
const path = require("path");
const fs = require("fs");
const util_1 = require("util");
const archiver = require("archiver");
const storage_1 = require("@google-cloud/storage");
const date_fns_1 = require("date-fns");
const execPromise = (0, util_1.promisify)(child_process_1.exec);
const fsPromises = fs.promises;
let BackupService = BackupService_1 = class BackupService {
    constructor() {
        this.logger = new common_1.Logger(BackupService_1.name);
        this.isBackupRunning = false;
        this.backupDir = path.join(process.cwd(), 'db_backup');
        this.tempDir = path.join(process.cwd(), 'db_backup', 'temp');
        const gcpKeyPath = path.join(process.cwd(), 'gcp-key-file.json');
        this.logger.log(`GCP Key Path: ${gcpKeyPath}`);
        if (process.env.GCP_PROJECT_ID && gcpKeyPath) {
            try {
                this.storage = new storage_1.Storage({
                    keyFilename: gcpKeyPath,
                    projectId: process.env.GCP_PROJECT_ID,
                });
                this.bucketName = process.env.GCP_BUCKET_NAME || 'db-backups';
                this.logger.log('GCP Storage client initialized successfully');
            }
            catch (error) {
                this.logger.error(`Failed to initialize GCP Storage client: ${error.message}`);
            }
        }
        else {
            this.logger.warn('GCP configuration not found. GCP upload will be skipped.');
        }
    }
    async onModuleInit() {
        this.ensureBackupDirExists();
        this.logger.log(`DB Backup Service initialized. Backups will be stored in: ${this.backupDir}`);
        try {
            await this.checkMongoDumpAvailability();
        }
        catch (error) {
            this.logger.error(`DB Backup Service initialization failed: ${error.message}`);
        }
    }
    ensureBackupDirExists() {
        if (!fs.existsSync(this.backupDir)) {
            fs.mkdirSync(this.backupDir, { recursive: true });
            this.logger.log(`Created backup directory: ${this.backupDir}`);
        }
        if (!fs.existsSync(this.tempDir)) {
            fs.mkdirSync(this.tempDir, { recursive: true });
            this.logger.log(`Created temp directory: ${this.tempDir}`);
        }
    }
    async checkMongoDumpAvailability() {
        try {
            await execPromise('mongodump --version');
            this.logger.log('mongodump command is available');
        }
        catch (error) {
            this.logger.error('mongodump command is not available. Database backups will not work.');
            this.logger.error('Please install MongoDB Database Tools: https://www.mongodb.com/try/download/database-tools');
            throw new Error('mongodump command not found');
        }
    }
    async scheduledBackup() {
        if (this.isBackupRunning) {
            this.logger.warn('Previous scheduled backup is still running. Skipping this cycle.');
            return;
        }
        this.isBackupRunning = true;
        if (process.env.IS_BACKUP !== 'true') {
            this.logger.log('Scheduled backup skipped: IS_BACKUP flag is not set to true');
            this.isBackupRunning = false;
            return;
        }
        this.logger.log('Starting scheduled database backup...');
        try {
            const backupPath = await this.createBackup();
            this.logger.log(`Database backup created at: ${backupPath}`);
            const zipFilePath = await this.createZipFromBackup(backupPath);
            this.logger.log(`Backup zip created at: ${zipFilePath}`);
            if (this.storage && this.bucketName) {
                try {
                    const uploadResult = await this.uploadToGCP(zipFilePath);
                    this.logger.log(`Backup uploaded to GCP: ${uploadResult}`);
                    await this.cleanupLocalBackups();
                }
                catch (uploadError) {
                    this.logger.error(`Failed to upload backup to GCP: ${uploadError.message}`);
                }
            }
            else {
                this.logger.warn('GCP upload skipped: GCP not configured');
                try {
                    await fsPromises.unlink(zipFilePath);
                    this.logger.log(`Cleaned up local zip file: ${zipFilePath}`);
                }
                catch (unlinkError) {
                    this.logger.warn(`Failed to clean up local zip file: ${unlinkError.message}`);
                }
            }
        }
        catch (error) {
            this.logger.error(`Scheduled backup failed: ${error.message}`);
        }
        finally {
            this.isBackupRunning = false;
        }
    }
    async cleanupOldBackups() {
        if (process.env.IS_BACKUP !== 'true') {
            this.logger.log('Scheduled backup skipped: IS_BACKUP flag is not set to true');
            return;
        }
        this.logger.log('Starting cleanup of old backups...');
        try {
            await this.cleanupLocalBackups();
            if (this.storage && this.bucketName) {
                await this.cleanupGCPBackups();
            }
            else {
                this.logger.warn('GCP cleanup skipped: GCP not configured');
            }
            this.logger.log('Backup cleanup completed successfully');
        }
        catch (error) {
            this.logger.error(`Backup cleanup failed: ${error.message}`);
        }
    }
    async cleanupLocalBackups() {
        try {
            this.logger.log('Cleaning up all local backup files for current environment...');
            const files = await fsPromises.readdir(this.backupDir);
            const envPrefix = `${process.env.APP_ENV}_db_backup-`;
            const backupDirs = files.filter(file => {
                const filePath = path.join(this.backupDir, file);
                const stats = fs.statSync(filePath);
                return stats.isDirectory() && file.startsWith(envPrefix);
            });
            let deletedCount = 0;
            for (const dir of backupDirs) {
                try {
                    const dirPath = path.join(this.backupDir, dir);
                    await this.retryRemoveDirectory(dirPath, 3, 1000);
                    deletedCount++;
                    this.logger.log(`Deleted backup: ${dir}`);
                }
                catch (error) {
                    this.logger.warn(`Failed to process backup directory ${dir}: ${error.message}`);
                }
            }
            try {
                const tempFiles = await fsPromises.readdir(this.tempDir);
                for (const file of tempFiles) {
                    if (file.endsWith('.zip')) {
                        try {
                            await this.retryUnlink(path.join(this.tempDir, file), 3, 1000);
                            this.logger.log(`Deleted temporary zip file: ${file}`);
                        }
                        catch (error) {
                            this.logger.warn(`Failed to delete temporary zip file ${file}: ${error.message}`);
                        }
                    }
                }
            }
            catch (error) {
                this.logger.warn(`Failed to clean up temp directory: ${error.message}`);
            }
            this.logger.log(`Local backup cleanup completed. Deleted ${deletedCount} backups.`);
        }
        catch (error) {
            this.logger.error(`Failed to clean up local backups: ${error.message}`);
            throw error;
        }
    }
    async retryRemoveDirectory(dirPath, retries, delayMs) {
        for (let attempt = 1; attempt <= retries; attempt++) {
            try {
                await this.removeDirectory(dirPath);
                return;
            }
            catch (error) {
                if (error.code === 'EBUSY' && attempt < retries) {
                    await new Promise(res => setTimeout(res, delayMs));
                }
                else {
                    throw error;
                }
            }
        }
    }
    async retryUnlink(filePath, retries, delayMs) {
        for (let attempt = 1; attempt <= retries; attempt++) {
            try {
                await fsPromises.unlink(filePath);
                return;
            }
            catch (error) {
                if (error.code === 'EBUSY' && attempt < retries) {
                    await new Promise(res => setTimeout(res, delayMs));
                }
                else {
                    throw error;
                }
            }
        }
    }
    async cleanupGCPBackups() {
        if (!this.storage || !this.bucketName) {
            throw new Error('GCP is not configured. Cannot clean up GCP backups.');
        }
        try {
            this.logger.log('Cleaning up GCP backup files...');
            const today = new Date();
            const cutoffDate = new Date(today.getFullYear(), today.getMonth(), today.getDate());
            const [files] = await this.storage.bucket(this.bucketName).getFiles({
                prefix: 'db-backups/',
            });
            let deletedCount = 0;
            for (const file of files) {
                try {
                    const fileName = path.basename(file.name);
                    if (!fileName.endsWith('.zip'))
                        continue;
                    const dateStr = fileName.replace('db_backup-', '').replace('.zip', '');
                    const backupDate = (0, date_fns_1.parseISO)(dateStr.replace(/-/g, (match, offset) => {
                        if (offset === 10)
                            return 'T';
                        if (offset === 13 || offset === 16)
                            return ':';
                        if (offset === 19)
                            return '.';
                        return match;
                    }));
                    if (!(0, date_fns_1.isAfter)(backupDate, cutoffDate)) {
                        await file.delete();
                        deletedCount++;
                        this.logger.log(`Deleted old GCP backup: ${fileName}`);
                    }
                }
                catch (error) {
                    this.logger.warn(`Failed to process GCP backup file ${file.name}: ${error.message}`);
                }
            }
            this.logger.log(`GCP backup cleanup completed. Deleted ${deletedCount} old backups.`);
        }
        catch (error) {
            this.logger.error(`Failed to clean up GCP backups: ${error.message}`);
            throw error;
        }
    }
    async removeDirectory(dirPath) {
        try {
            const entries = await fsPromises.readdir(dirPath, { withFileTypes: true });
            for (const entry of entries) {
                const fullPath = path.join(dirPath, entry.name);
                if (entry.isDirectory()) {
                    await this.removeDirectory(fullPath);
                }
                else {
                    await fsPromises.unlink(fullPath);
                }
            }
            await fsPromises.rmdir(dirPath);
        }
        catch (error) {
            this.logger.error(`Failed to remove directory ${dirPath}: ${error.message}`);
            throw error;
        }
    }
    async createBackup() {
        this.ensureBackupDirExists();
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const backupPath = path.join(this.backupDir, `${process.env.APP_ENV}_db_backup-${timestamp}`);
        const mongoUri = process.env.MONGO_URI || process.env.MONGODB_URI;
        if (!mongoUri) {
            throw new Error('MongoDB URI not found in environment variables');
        }
        const dumpCommand = `mongodump --uri="${mongoUri}" --out="${backupPath}"`;
        try {
            const { stderr } = await execPromise(dumpCommand);
            if (stderr && !stderr.includes('done dumping')) {
                this.logger.warn(`Warning during database backup: ${stderr}`);
            }
            return backupPath;
        }
        catch (error) {
            this.logger.error(`Backup failed: ${error.message}`);
            throw new Error(`Backup failed: ${error.message}`);
        }
    }
    async createZipFromBackup(backupPath) {
        const backupDirName = path.basename(backupPath);
        const zipFilePath = path.join(this.tempDir, `${backupDirName}.zip`);
        return new Promise((resolve, reject) => {
            const output = fs.createWriteStream(zipFilePath);
            const archive = archiver('zip', {
                zlib: { level: 9 }
            });
            output.on('close', () => {
                this.logger.log(`Zip file created: ${zipFilePath} (${archive.pointer()} bytes)`);
                resolve(zipFilePath);
            });
            archive.on('error', (err) => {
                reject(new Error(`Failed to create zip file: ${err.message}`));
            });
            archive.pipe(output);
            archive.directory(backupPath, false);
            archive.finalize();
        });
    }
    async uploadToGCP(filePath) {
        if (!this.storage || !this.bucketName) {
            throw new Error('GCP is not configured. Cannot upload to GCP.');
        }
        try {
            const [bucketExists] = await this.storage.bucket(this.bucketName).exists();
            if (!bucketExists) {
                this.logger.log(`Bucket ${this.bucketName} does not exist. Creating...`);
                await this.storage.createBucket(this.bucketName);
                this.logger.log(`Bucket ${this.bucketName} created.`);
            }
            const fileName = path.basename(filePath);
            const destination = `db-backups/${fileName}`;
            await this.storage.bucket(this.bucketName).upload(filePath, {
                destination,
                metadata: {
                    contentType: 'application/zip',
                },
            });
            const publicUrl = `https://storage.googleapis.com/${this.bucketName}/${destination}`;
            await fsPromises.unlink(filePath);
            return publicUrl;
        }
        catch (error) {
            this.logger.error(`Failed to upload to GCP: ${error.message}`);
            throw new Error(`Failed to upload to GCP: ${error.message}`);
        }
    }
};
exports.BackupService = BackupService;
__decorate([
    (0, schedule_1.Cron)(schedule_1.CronExpression.EVERY_4_HOURS),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], BackupService.prototype, "scheduledBackup", null);
__decorate([
    (0, schedule_1.Cron)(schedule_1.CronExpression.EVERY_DAY_AT_MIDNIGHT),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], BackupService.prototype, "cleanupOldBackups", null);
exports.BackupService = BackupService = BackupService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [])
], BackupService);
//# sourceMappingURL=backup.service.js.map