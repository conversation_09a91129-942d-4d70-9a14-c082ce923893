"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OauthModule = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const ouath_service_1 = require("./ouath.service");
const oauth_controller_1 = require("./oauth.controller");
const jwt_1 = require("@nestjs/jwt");
const authorization_schema_1 = require("./schemas/authorization.schema");
const logs_module_1 = require("../modules/logs-module/logs.module");
let OauthModule = class OauthModule {
};
exports.OauthModule = OauthModule;
exports.OauthModule = OauthModule = __decorate([
    (0, common_1.Module)({
        imports: [
            mongoose_1.MongooseModule.forFeature([{ name: authorization_schema_1.Authorization.name, schema: authorization_schema_1.AuthorizationSchema }]),
            jwt_1.JwtModule.register({
                secret: 'xyz',
                signOptions: { expiresIn: '60s' },
            }),
            logs_module_1.LogsModule
        ],
        controllers: [oauth_controller_1.OauthController],
        providers: [ouath_service_1.AuthorizationService, logs_module_1.LogsModule],
        exports: [ouath_service_1.AuthorizationService],
    })
], OauthModule);
//# sourceMappingURL=oauth.module.js.map