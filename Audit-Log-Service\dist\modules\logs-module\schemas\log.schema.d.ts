import { Schema, Document } from 'mongoose';
export interface Log extends Document {
    userId: string;
    action: string;
    resource: string;
    metadata: Record<string, any>;
    [key: string]: any;
}
export declare const LogSchema: Schema<Log, import("mongoose").Model<Log, any, any, any, Document<unknown, any, Log, any> & Log & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Log, Document<unknown, {}, import("mongoose").FlatRecord<Log>, {}> & import("mongoose").FlatRecord<Log> & Required<{
    _id: unknown;
}> & {
    __v: number;
}>;
