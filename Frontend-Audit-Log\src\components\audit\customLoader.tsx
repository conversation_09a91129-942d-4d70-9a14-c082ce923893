import { getQueryParams } from '@/services/api';


export function CustomLoader() {
  const { state } = getQueryParams();
  const logoSrc = state === 'CT-RAD' ? '/radfavicon.ico' : '/favicon.ico';

  return (
    <div className="flex items-center justify-center h-screen w-screen bg-white fixed top-0 left-0">
      <div className="relative flex items-center justify-center w-16 h-16">
        <img src={logoSrc} alt="Logo" className="absolute w-12 h-12" />
        <div className="w-16 h-16 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
      </div>
    </div>
  );
}
