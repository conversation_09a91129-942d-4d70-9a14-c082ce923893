"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LogsController = void 0;
const common_1 = require("@nestjs/common");
const logs_service_1 = require("./logs.service");
const api_key_guard_1 = require("../../jwt_guard/api-key.guard");
const skipauth_decorators_1 = require("../../jwt_guard/decorators/skipauth.decorators");
let LogsController = class LogsController {
    constructor(logsService) {
        this.logsService = logsService;
    }
    async createLog(log, req) {
        try {
            const apiKey = req.headers['api-key'];
            const serviceKey = req.headers['servicekey'];
            if (!apiKey || apiKey !== process.env.API_SECRET_KEY) {
                return {
                    statusCode: common_1.HttpStatus.UNAUTHORIZED,
                    message: 'Invalid API key',
                };
            }
            const createdLog = await this.logsService.createLog(log, serviceKey);
            return {
                statusCode: common_1.HttpStatus.CREATED,
                message: 'Log created successfully',
                data: createdLog,
            };
        }
        catch (error) {
            return {
                statusCode: common_1.HttpStatus.INTERNAL_SERVER_ERROR,
                message: 'Failed to create log',
                error: error.message,
            };
        }
    }
    async getLogs(query) {
        try {
            const logs = await this.logsService.getLogs(query);
            return {
                statusCode: common_1.HttpStatus.OK,
                message: 'Logs fetched successfully',
                data: logs,
            };
        }
        catch (error) {
            return {
                statusCode: common_1.HttpStatus.INTERNAL_SERVER_ERROR,
                message: 'Error fetching logs',
                error: error.message,
            };
        }
    }
    async getLogsByResource(resource) {
        try {
            const logs = await this.logsService.getLogsByResource(resource);
            return {
                statusCode: common_1.HttpStatus.OK,
                message: 'Logs fetched successfully by resource',
                data: logs,
            };
        }
        catch (error) {
            return {
                statusCode: common_1.HttpStatus.INTERNAL_SERVER_ERROR,
                message: 'Error fetching logs by resource',
                error: error.message,
            };
        }
    }
    async searchWithDynamicFilters(filters, skip, limit, sort, search, service, userId) {
        try {
            const parsedFilters = filters ? JSON.parse(filters) : {};
            const skipNum = skip ? Number(skip) : 0;
            const limitNum = limit ? Number(limit) : 10;
            const serviceKey = Number(service);
            if (userId) {
                parsedFilters.userId = userId;
            }
            if (search) {
                parsedFilters.$or = [
                    { userName: { $regex: search, $options: 'i' } },
                    { userType: { $regex: search, $options: 'i' } },
                    { userId: { $regex: search, $options: 'i' } },
                    { action: { $regex: search, $options: 'i' } },
                    { resource: { $regex: search, $options: 'i' } },
                    { message: { $regex: search, $options: 'i' } },
                ];
            }
            const logs = await this.logsService.findWithDynamicFilters(parsedFilters, skipNum, limitNum, sort || 'createdAt', serviceKey);
            return {
                statusCode: common_1.HttpStatus.OK,
                message: 'Logs fetched successfully with dynamic filters',
                data: logs,
            };
        }
        catch (error) {
            return {
                statusCode: common_1.HttpStatus.INTERNAL_SERVER_ERROR,
                message: 'Error fetching logs with dynamic filters',
                error: error.message,
            };
        }
    }
    async getFilters(serviceKey, schoolId) {
        try {
            const schoolFilter = await this.logsService.getSchoolFilter(serviceKey);
            const actionFilter = await this.logsService.getActionFilter(serviceKey);
            const resourceFilter = await this.logsService.getResourceFilter(serviceKey);
            const userFilter = await this.logsService.getUserFilter(serviceKey, schoolId);
            return {
                statusCode: common_1.HttpStatus.OK,
                message: 'filters fetched successfully',
                filters: {
                    schoolFilter,
                    actionFilter,
                    resourceFilter,
                    userFilter,
                },
            };
        }
        catch (error) {
            return {
                statusCode: common_1.HttpStatus.INTERNAL_SERVER_ERROR,
                message: 'Error fetching filters',
                error: error.message,
            };
        }
    }
};
exports.LogsController = LogsController;
__decorate([
    (0, skipauth_decorators_1.SkipAuth)(),
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Request]),
    __metadata("design:returntype", Promise)
], LogsController.prototype, "createLog", null);
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], LogsController.prototype, "getLogs", null);
__decorate([
    (0, common_1.Get)('logs'),
    __param(0, (0, common_1.Query)('resource')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], LogsController.prototype, "getLogsByResource", null);
__decorate([
    (0, common_1.UseGuards)(api_key_guard_1.JwtAuthGuard),
    (0, common_1.Get)('dynamic-search'),
    __param(0, (0, common_1.Query)('filters')),
    __param(1, (0, common_1.Query)('skip')),
    __param(2, (0, common_1.Query)('limit')),
    __param(3, (0, common_1.Query)('sort')),
    __param(4, (0, common_1.Query)('search')),
    __param(5, (0, common_1.Query)('serviceKey')),
    __param(6, (0, common_1.Query)('userId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, String, String, String, String]),
    __metadata("design:returntype", Promise)
], LogsController.prototype, "searchWithDynamicFilters", null);
__decorate([
    (0, common_1.UseGuards)(api_key_guard_1.JwtAuthGuard),
    (0, common_1.Get)('filters-list'),
    __param(0, (0, common_1.Query)('serviceKey')),
    __param(1, (0, common_1.Query)('schoolId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], LogsController.prototype, "getFilters", null);
exports.LogsController = LogsController = __decorate([
    (0, common_1.Controller)('logs'),
    __metadata("design:paramtypes", [logs_service_1.LogsService])
], LogsController);
//# sourceMappingURL=logs.controller.js.map