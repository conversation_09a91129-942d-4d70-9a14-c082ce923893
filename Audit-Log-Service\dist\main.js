"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
require("./polyfills");
const core_1 = require("@nestjs/core");
const app_module_1 = require("./app.module");
const config_1 = require("@nestjs/config");
const common_1 = require("@nestjs/common");
async function bootstrap() {
    const app = await core_1.NestFactory.create(app_module_1.AppModule);
    const config = app.get(config_1.ConfigService);
    const port = config.get('PORT');
    app.enableCors({
        origin: '*',
    });
    app.useGlobalPipes();
    await app.listen(port, () => {
        common_1.Logger.log(`Application listening on port ${port}`, 'Main');
    });
}
bootstrap();
//# sourceMappingURL=main.js.map