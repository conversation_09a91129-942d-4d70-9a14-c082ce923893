"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var AppModule_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const mongoose_1 = require("@nestjs/mongoose");
const logs_module_1 = require("./modules/logs-module/logs.module");
const oauth_module_1 = require("./ouath/oauth.module");
const register_client_module_1 = require("./modules/register-client/register-client.module");
const config_2 = require("@nestjs/config");
const core_1 = require("@nestjs/core");
const jwt_1 = require("@nestjs/jwt");
const api_key_guard_1 = require("./jwt_guard/api-key.guard");
const app_controller_1 = require("./app.controller");
const mongoose_2 = require("mongoose");
const backup_controller_1 = require("./backup/backup.controller");
const backup_module_1 = require("./backup/backup.module");
let AppModule = AppModule_1 = class AppModule {
    constructor(primaryConnection, secondaryConnection) {
        this.primaryConnection = primaryConnection;
        this.secondaryConnection = secondaryConnection;
        this.logger = new common_1.Logger(AppModule_1.name);
    }
    onModuleInit() {
        this.primaryConnection.once('open', () => {
            this.logger.log('✅ Connected to Primary MongoDB');
        });
        this.secondaryConnection.once('open', () => {
            this.logger.log('✅ Connected to Secondary MongoDB');
        });
        this.primaryConnection.on('error', (err) => {
            this.logger.error('❌ Primary MongoDB Connection Error:', err);
        });
        this.secondaryConnection.on('error', (err) => {
            this.logger.error('❌ Secondary MongoDB Connection Error:', err);
        });
    }
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = AppModule_1 = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({ isGlobal: true }),
            mongoose_1.MongooseModule.forRootAsync({
                imports: [config_1.ConfigModule],
                useFactory: async (configService) => {
                    const mongoUrl = configService.get('MONGO_URI');
                    return {
                        uri: mongoUrl,
                        useNewUrlParser: true,
                        useUnifiedTopology: true,
                    };
                },
                inject: [config_2.ConfigService],
            }),
            mongoose_1.MongooseModule.forRootAsync({
                imports: [config_1.ConfigModule],
                useFactory: async (configService) => ({
                    uri: configService.get('MONGO_URI_RADIOLOGY'),
                    useNewUrlParser: true,
                    useUnifiedTopology: true,
                }),
                inject: [config_2.ConfigService],
                connectionName: 'radiologyConnection',
            }),
            jwt_1.JwtModule.registerAsync({
                imports: [config_1.ConfigModule],
                useFactory: async (configService) => ({
                    secret: configService.get('JWT_ACCESS_SECRET'),
                    signOptions: { expiresIn: '1h' },
                }),
                inject: [config_2.ConfigService],
            }),
            logs_module_1.LogsModule,
            oauth_module_1.OauthModule,
            register_client_module_1.RegisterClientModule,
            backup_module_1.BackupModule
        ],
        providers: [
            {
                provide: core_1.APP_GUARD,
                useClass: api_key_guard_1.JwtAuthGuard,
            },
        ],
        controllers: [
            app_controller_1.AppController,
            backup_controller_1.BackupController
        ]
    }),
    __param(0, (0, mongoose_1.InjectConnection)()),
    __param(1, (0, mongoose_1.InjectConnection)('radiologyConnection')),
    __metadata("design:paramtypes", [mongoose_2.Connection,
        mongoose_2.Connection])
], AppModule);
//# sourceMappingURL=app.module.js.map