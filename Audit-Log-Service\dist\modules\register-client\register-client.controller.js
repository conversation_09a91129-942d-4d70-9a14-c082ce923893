"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RegisterClientController = void 0;
const common_1 = require("@nestjs/common");
const register_client_service_1 = require("./register-client.service");
let RegisterClientController = class RegisterClientController {
    constructor(registerClientService) {
        this.registerClientService = registerClientService;
    }
    async registerClient(applicationName, description) {
        return await this.registerClientService.registerClient(applicationName, description);
    }
    async updateClient(apiKey, updates) {
        return await this.registerClientService.updateClient(apiKey, updates);
    }
    async deactivateClient(apiKey) {
        return await this.registerClientService.deactivateClient(apiKey);
    }
};
exports.RegisterClientController = RegisterClientController;
__decorate([
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)('applicationName')),
    __param(1, (0, common_1.Body)('description')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], RegisterClientController.prototype, "registerClient", null);
__decorate([
    (0, common_1.Put)(':apiKey'),
    __param(0, (0, common_1.Param)('apiKey')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], RegisterClientController.prototype, "updateClient", null);
__decorate([
    (0, common_1.Put)(':apiKey/deactivate'),
    __param(0, (0, common_1.Param)('apiKey')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], RegisterClientController.prototype, "deactivateClient", null);
exports.RegisterClientController = RegisterClientController = __decorate([
    (0, common_1.Controller)('register-client'),
    __metadata("design:paramtypes", [register_client_service_1.RegisterClientService])
], RegisterClientController);
//# sourceMappingURL=register-client.controller.js.map