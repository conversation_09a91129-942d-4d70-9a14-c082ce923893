import { <PERSON><PERSON>, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Schema as MongooseSchema, Document } from 'mongoose';

@Schema({
  timestamps: true, // Adds createdAt and updatedAt fields
  strict: false,    // Allows additional fields at the root level
})
export class AuditLog extends Document {
  @Prop({ required: true })
  userId: string;

  @Prop({ required: true })
  action: string;

  @Prop({ required: true })
  resource: string;

  @Prop({ type: MongooseSchema.Types.Mixed }) // Allows dynamic nested objects
  metadata: Record<string, any>;

  // Additional root-level dynamic keys are allowed by `strict: false`
}

export const AuditLogSchema = SchemaFactory.createForClass(AuditLog);
