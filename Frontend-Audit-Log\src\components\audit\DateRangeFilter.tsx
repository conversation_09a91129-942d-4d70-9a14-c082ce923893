import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import { Cross1Icon, CalendarIcon } from '@radix-ui/react-icons';
import { DateRange } from 'react-day-picker';
import { DateRangeType } from '@/types/audit';
import { format, endOfDay, startOfDay } from 'date-fns';

export interface DateRangeFilterProps {
  dateRange: DateRange | undefined;
  onDateRangeChange: (range: DateRange | undefined) => void;
  selectedRange: DateRangeType;
  onRangeTypeChange: (type: DateRangeType) => void;
}

export function DateRangeFilter({
  dateRange,
  onDateRangeChange,
  selectedRange,
  onRangeTypeChange,
}: DateRangeFilterProps) {
  const handleClear = () => {
    onDateRangeChange(undefined);
    onRangeTypeChange('selectdays');
  };

  const handleSelect = (range: DateRange | undefined) => {
    if (!range) {
      handleClear();
      return;
    }

    // If only from date is selected, set both from and to to the same date
    if (range.from && !range.to) {
      const sameDay = {
        from: startOfDay(range.from),
        to: endOfDay(range.from)
      };
      onDateRangeChange(sameDay);
      onRangeTypeChange('custom');
      return;
    }

    // Set the full day range
    const fullDayRange = {
      from: range.from ? startOfDay(range.from) : undefined,
      to: range.to ? endOfDay(range.to) : undefined
    };
    
    onDateRangeChange(fullDayRange);
    onRangeTypeChange('custom');
  };

  const getDisplayText = () => {
    if (!dateRange?.from) return 'Pick a date range';
    
    if (!dateRange.to || dateRange.from === dateRange.to) {
      return format(dateRange.from, 'LLL dd, y');
    }
    
    return `${format(dateRange.from, 'LLL dd, y')} - ${format(dateRange.to, 'LLL dd, y')}`;
  };

  return (
    <div className="space-y-2">
      <h3 className="text-sm font-medium">Custom Date Range</h3>
      <div className="flex items-center">
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className={cn(
                'justify-start text-left font-normal w-full max-w-[280px] text-foreground',
                !dateRange && 'text-muted-foreground'
              )}
            >
              <CalendarIcon className="mr-2 h-4 w-4" />
              {getDisplayText()}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <Calendar 
              className="bg-white max-w-[300px]"
              initialFocus
              mode="range"
              defaultMonth={dateRange?.from}
              selected={dateRange}
              onSelect={handleSelect}
              numberOfMonths={1}
              disabled={{ after: new Date() }}  // Prevents future date selection
            />
          </PopoverContent>
        </Popover>
        {(dateRange || selectedRange !== 'selectdays') && (
          <button
            type="button"
            className="p-1.5 rounded-full bg-slate-50 hover:bg-gray-100"
            onClick={handleClear}
          >
            <Cross1Icon className="h-4 w-4 text-gray-500" />
          </button>
        )}
      </div>
    </div>
  );
}