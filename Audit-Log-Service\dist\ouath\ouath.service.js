"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthorizationService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
let AuthorizationService = class AuthorizationService {
    constructor(authModel) {
        this.authModel = authModel;
    }
    async saveAuthCode(authCodeData) {
        const authCode = new this.authModel(authCodeData);
        return authCode.save();
    }
    async findAuthCode(authCode) {
        return this.authModel.findOne({ authCode, isExpired: false }).exec();
    }
    async expireAuthCode(authCode) {
        await this.authModel.updateOne({ authCode }, { isExpired: true }).exec();
    }
};
exports.AuthorizationService = AuthorizationService;
exports.AuthorizationService = AuthorizationService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)('Authorization')),
    __metadata("design:paramtypes", [mongoose_2.Model])
], AuthorizationService);
//# sourceMappingURL=ouath.service.js.map