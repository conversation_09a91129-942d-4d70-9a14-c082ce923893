import { Schema as MongooseSchema, Document } from 'mongoose';
export declare class RadiologyLog extends Document {
    userId: string;
    action: string;
    resource: string;
    metadata: Record<string, any>;
}
export declare const RadiologySchema: MongooseSchema<RadiologyLog, import("mongoose").Model<RadiologyLog, any, any, any, Document<unknown, any, RadiologyLog, any> & RadiologyLog & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, RadiologyLog, Document<unknown, {}, import("mongoose").FlatRecord<RadiologyLog>, {}> & import("mongoose").FlatRecord<RadiologyLog> & Required<{
    _id: unknown;
}> & {
    __v: number;
}>;
