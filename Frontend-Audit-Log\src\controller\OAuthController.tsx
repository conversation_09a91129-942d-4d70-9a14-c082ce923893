// import React, { useEffect, useState } from "react";
// import { useNavigate } from "react-router-dom";
// import { fetchTokens, fetchAuditLogs } from "@/services/api";

// const OAuthController: React.FC = () => {
//   const navigate = useNavigate();
//   const [errorMessage, setErrorMessage] = useState("");

//   useEffect(() => {
//     const queryParams = new URLSearchParams(window.location.search);
//     const authorizationCode = queryParams.get("authorization_code");
//     const state = queryParams.get("state");
//     const schoolId = queryParams.get("school_id");

//     if (authorizationCode && state && schoolId) {
//       fetchTokens(authorizationCode, state, schoolId)
//         .then((tokens) => {
//           if (tokens?.accessToken && tokens?.refreshToken) {
//             localStorage.setItem("accessToken", tokens.accessToken);
//             localStorage.setItem("refreshToken", tokens.refreshToken);
//             navigate("/dashboard");
//           } else {
//             setErrorMessage("Failed to retrieve tokens. Please try again.");
//           }
//         })
//         .catch((error) => {
//           console.error(error);
//           setErrorMessage("An error occurred during authentication.");
//         });
//     } else {
//       setErrorMessage("Invalid or missing query parameters.");
//     }
//   }, [navigate]);

//   return errorMessage ? <p>{errorMessage}</p> : <p>Loading...</p>;
// };

// export default OAuthController;
