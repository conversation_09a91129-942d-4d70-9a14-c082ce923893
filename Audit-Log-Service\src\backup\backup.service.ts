import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { exec } from 'child_process';
import * as path from 'path';
import * as fs from 'fs';
import { promisify } from 'util';
import * as archiver from 'archiver';
import { Storage } from '@google-cloud/storage';
import { subDays, isAfter, parseISO } from 'date-fns';

const execPromise = promisify(exec);
const fsPromises = fs.promises;

@Injectable()
export class BackupService implements OnModuleInit {
  private readonly logger = new Logger(BackupService.name);
  private backupDir: string;
  private tempDir: string;
  private storage: Storage;
  private bucketName: string;
  private isBackupRunning = false; // Add a lock to prevent overlapping backups

  constructor() {
    // Set the backup directory path to project root/db_backup
    this.backupDir = path.join(process.cwd(), 'db_backup');
    this.tempDir = path.join(process.cwd(), 'db_backup', 'temp');

    // Load GCP key file from base path
    const gcpKeyPath = path.join(process.cwd(), 'gcp-key-file.json');

    this.logger.log(`GCP Key Path: ${gcpKeyPath}`);

    // Initialize Google Cloud Storage client if GCP config is available
    if (process.env.GCP_PROJECT_ID && gcpKeyPath) {
      try {
        this.storage = new Storage({
          keyFilename: gcpKeyPath,
          projectId: process.env.GCP_PROJECT_ID,
        });

        this.bucketName = process.env.GCP_BUCKET_NAME || 'db-backups';
        this.logger.log('GCP Storage client initialized successfully');
      } catch (error) {
        this.logger.error(`Failed to initialize GCP Storage client: ${error.message}`);
      }
    } else {
      this.logger.warn('GCP configuration not found. GCP upload will be skipped.');
    }
  }

  async onModuleInit() {
    // Create backup directory if it doesn't exist
    this.ensureBackupDirExists();
    this.logger.log(`DB Backup Service initialized. Backups will be stored in: ${this.backupDir}`);

    // Check if mongodump is available
    try {
      await this.checkMongoDumpAvailability();
    } catch (error) {
      this.logger.error(`DB Backup Service initialization failed: ${error.message}`);
    }
  }

  private ensureBackupDirExists() {
    // Create main backup directory if it doesn't exist
    if (!fs.existsSync(this.backupDir)) {
      fs.mkdirSync(this.backupDir, { recursive: true });
      this.logger.log(`Created backup directory: ${this.backupDir}`);
    }

    // Create temp directory for processing if it doesn't exist
    if (!fs.existsSync(this.tempDir)) {
      fs.mkdirSync(this.tempDir, { recursive: true });
      this.logger.log(`Created temp directory: ${this.tempDir}`);
    }
  }

  private async checkMongoDumpAvailability(): Promise<void> {
    try {
      await execPromise('mongodump --version');
      this.logger.log('mongodump command is available');
    } catch (error) {
      this.logger.error('mongodump command is not available. Database backups will not work.');
      this.logger.error('Please install MongoDB Database Tools: https://www.mongodb.com/try/download/database-tools');
      throw new Error('mongodump command not found');
    }
  }

  @Cron(CronExpression.EVERY_4_HOURS)
  async scheduledBackup() {
    if (this.isBackupRunning) {
      this.logger.warn('Previous scheduled backup is still running. Skipping this cycle.');
      return;
    }
    this.isBackupRunning = true;
    if (process.env.IS_BACKUP !== 'true') {
      this.logger.log('Scheduled backup skipped: IS_BACKUP flag is not set to true');
      this.isBackupRunning = false;
      return;
    }
    this.logger.log('Starting scheduled database backup...');
    try {
      // Create the database backup
      const backupPath = await this.createBackup();
      this.logger.log(`Database backup created at: ${backupPath}`);

      // Create a zip file of the backup
      const zipFilePath = await this.createZipFromBackup(backupPath);
      this.logger.log(`Backup zip created at: ${zipFilePath}`);

      // Upload the zip file to GCP if configured
      if (this.storage && this.bucketName) {
        try {
          const uploadResult = await this.uploadToGCP(zipFilePath);
          this.logger.log(`Backup uploaded to GCP: ${uploadResult}`);
          // After successful upload, delete all local backups and zip files
          await this.cleanupLocalBackups();
        } catch (uploadError) {
          this.logger.error(`Failed to upload backup to GCP: ${uploadError.message}`);
          // Don't rethrow the error, continue with the backup process
        }
      } else {
        this.logger.warn('GCP upload skipped: GCP not configured');
        // If we're not uploading to GCP, we should clean up the zip file ourselves
        try {
          await fsPromises.unlink(zipFilePath);
          this.logger.log(`Cleaned up local zip file: ${zipFilePath}`);
        } catch (unlinkError) {
          this.logger.warn(`Failed to clean up local zip file: ${unlinkError.message}`);
        }
      }
    } catch (error) {
      this.logger.error(`Scheduled backup failed: ${error.message}`);
    } finally {
      this.isBackupRunning = false;
    }
  }

  /**
   * Cron job to clean up old backups, keeping only the current day's and yesterday's backups
   * Runs daily at midnight and can also be triggered manually
   */
  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async cleanupOldBackups() {
    if (process.env.IS_BACKUP !== 'true') {
      this.logger.log('Scheduled backup skipped: IS_BACKUP flag is not set to true');
      return;
    }
    this.logger.log('Starting cleanup of old backups...');

    try {
      // Clean up local backups
      await this.cleanupLocalBackups();

      // Clean up GCP backups if configured
      if (this.storage && this.bucketName) {
        await this.cleanupGCPBackups();
      } else {
        this.logger.warn('GCP cleanup skipped: GCP not configured');
      }

      this.logger.log('Backup cleanup completed successfully');
    } catch (error) {
      this.logger.error(`Backup cleanup failed: ${error.message}`);
    }
  }

  /**
   * Cleans up all local backup files and zip files.
   */
  private async cleanupLocalBackups(): Promise<void> {
    try {
      this.logger.log('Cleaning up all local backup files for current environment...');
      const files = await fsPromises.readdir(this.backupDir);
      // Only match directories that start with the current APP_ENV prefix
      const envPrefix = `${process.env.APP_ENV}_db_backup-`;
      const backupDirs = files.filter(file => {
        const filePath = path.join(this.backupDir, file);
        const stats = fs.statSync(filePath);
        return stats.isDirectory() && file.startsWith(envPrefix);
      });
      let deletedCount = 0;
      for (const dir of backupDirs) {
        try {
          const dirPath = path.join(this.backupDir, dir);
          await this.retryRemoveDirectory(dirPath, 3, 1000); // Retry up to 3 times with 1s delay
          deletedCount++;
          this.logger.log(`Deleted backup: ${dir}`);
        } catch (error) {
          this.logger.warn(`Failed to process backup directory ${dir}: ${error.message}`);
        }
      }
      // Also clean up the temp directory
      try {
        const tempFiles = await fsPromises.readdir(this.tempDir);
        for (const file of tempFiles) {
          if (file.endsWith('.zip')) {
            try {
              await this.retryUnlink(path.join(this.tempDir, file), 3, 1000);
              this.logger.log(`Deleted temporary zip file: ${file}`);
            } catch (error) {
              this.logger.warn(`Failed to delete temporary zip file ${file}: ${error.message}`);
            }
          }
        }
      } catch (error) {
        this.logger.warn(`Failed to clean up temp directory: ${error.message}`);
      }
      this.logger.log(`Local backup cleanup completed. Deleted ${deletedCount} backups.`);
    } catch (error) {
      this.logger.error(`Failed to clean up local backups: ${error.message}`);
      throw error;
    }
  }

  // Retry directory removal for EBUSY errors
  private async retryRemoveDirectory(dirPath: string, retries: number, delayMs: number): Promise<void> {
    for (let attempt = 1; attempt <= retries; attempt++) {
      try {
        await this.removeDirectory(dirPath);
        return;
      } catch (error) {
        if (error.code === 'EBUSY' && attempt < retries) {
          await new Promise(res => setTimeout(res, delayMs));
        } else {
          throw error;
        }
      }
    }
  }

  // Retry file unlink for EBUSY errors
  private async retryUnlink(filePath: string, retries: number, delayMs: number): Promise<void> {
    for (let attempt = 1; attempt <= retries; attempt++) {
      try {
        await fsPromises.unlink(filePath);
        return;
      } catch (error) {
        if (error.code === 'EBUSY' && attempt < retries) {
          await new Promise(res => setTimeout(res, delayMs));
        } else {
          throw error;
        }
      }
    }
  }

  /**
   * Cleans up old backup files from the GCP bucket
   * Keeps only the current day's and yesterday's backups
   */
  private async cleanupGCPBackups(): Promise<void> {
    if (!this.storage || !this.bucketName) {
      throw new Error('GCP is not configured. Cannot clean up GCP backups.');
    }

    try {
      this.logger.log('Cleaning up GCP backup files...');

      // Get the cutoff date (yesterday's start)
      // const today = new Date();
      // const yesterday = subDays(today, 1);
      // const cutoffDate = new Date(
      //   yesterday.getFullYear(),
      //   yesterday.getMonth(),
      //   yesterday.getDate()
      // );

      // Keep only today's backups
      const today = new Date();
      const cutoffDate = new Date(
        today.getFullYear(),
        today.getMonth(),
        today.getDate()
      );

      // Get all files in the bucket's db-backups directory
      const [files] = await this.storage.bucket(this.bucketName).getFiles({
        prefix: 'db-backups/',
      });

      // Count of deleted files
      let deletedCount = 0;

      // Process each backup file
      for (const file of files) {
        try {
          const fileName = path.basename(file.name);

          // Skip if not a zip file
          if (!fileName.endsWith('.zip')) continue;

          // Extract the date from the file name
          // Format is db_backup-YYYY-MM-DDThh-mm-ss-SSSZ.zip
          const dateStr = fileName.replace('db_backup-', '').replace('.zip', '');
          const backupDate = parseISO(dateStr.replace(/-/g, (match, offset) => {
            // Replace hyphens with colons and periods as needed to reconstruct ISO format
            if (offset === 10) return 'T'; // Replace the hyphen after date with T
            if (offset === 13 || offset === 16) return ':'; // Replace hyphens in time with colons
            if (offset === 19) return '.'; // Replace hyphen before milliseconds with period
            return match; // Keep other hyphens
          }));

          // If the backup is older than the cutoff date, delete it
          if (!isAfter(backupDate, cutoffDate)) {
            await file.delete();
            deletedCount++;
            this.logger.log(`Deleted old GCP backup: ${fileName}`);
          }
        } catch (error) {
          this.logger.warn(`Failed to process GCP backup file ${file.name}: ${error.message}`);
        }
      }

      this.logger.log(`GCP backup cleanup completed. Deleted ${deletedCount} old backups.`);
    } catch (error) {
      this.logger.error(`Failed to clean up GCP backups: ${error.message}`);
      throw error;
    }
  }

  /**
   * Recursively removes a directory and all its contents
   * @param dirPath Path to the directory to remove
   */
  private async removeDirectory(dirPath: string): Promise<void> {
    try {
      const entries = await fsPromises.readdir(dirPath, { withFileTypes: true });

      // Process all entries in the directory
      for (const entry of entries) {
        const fullPath = path.join(dirPath, entry.name);

        if (entry.isDirectory()) {
          // Recursively remove subdirectories
          await this.removeDirectory(fullPath);
        } else {
          // Remove files
          await fsPromises.unlink(fullPath);
        }
      }

      // Remove the empty directory
      await fsPromises.rmdir(dirPath);
    } catch (error) {
      this.logger.error(`Failed to remove directory ${dirPath}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Creates a database backup using mongodump
   * @returns Path to the created backup directory
   */
  async createBackup(): Promise<string> {
    this.ensureBackupDirExists();

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupPath = path.join(this.backupDir, `${process.env.APP_ENV}_db_backup-${timestamp}`);

    // Get MongoDB URI from environment variables
    const mongoUri = process.env.MONGO_URI || process.env.MONGODB_URI;

    if (!mongoUri) {
      throw new Error('MongoDB URI not found in environment variables');
    }

    //  this.logger.log(`mongoUri  ${mongoUri}`);
    const dumpCommand = `mongodump --uri="${mongoUri}" --out="${backupPath}"`;
    // const dumpCommand = `mongodump --uri="************************************************************************************" --out="${backupPath}"`;

    try {
      const { stderr } = await execPromise(dumpCommand);

      if (stderr && !stderr.includes('done dumping')) {
        this.logger.warn(`Warning during database backup: ${stderr}`);
      }

      // Return the path to the backup directory
      return backupPath;
    } catch (error) {
      this.logger.error(`Backup failed: ${error.message}`);
      throw new Error(`Backup failed: ${error.message}`);
    }
  }

   /**
   * Creates a zip file from the backup directory
   * @param backupPath Path to the backup directory
   * @returns Path to the created zip file
   */
  async createZipFromBackup(backupPath: string): Promise<string> {
    const backupDirName = path.basename(backupPath);
    const zipFilePath = path.join(this.tempDir, `${backupDirName}.zip`);

    return new Promise((resolve, reject) => {
      // Create a file to write the zip to
      const output = fs.createWriteStream(zipFilePath);
      const archive = archiver('zip', {
        zlib: { level: 9 } // Maximum compression
      });

      // Listen for all archive data to be written
      output.on('close', () => {
        this.logger.log(`Zip file created: ${zipFilePath} (${archive.pointer()} bytes)`);
        resolve(zipFilePath);
      });

      // Handle errors
      archive.on('error', (err) => {
        reject(new Error(`Failed to create zip file: ${err.message}`));
      });

      // Pipe archive data to the file
      archive.pipe(output);

      // Add the backup directory to the archive
      archive.directory(backupPath, false);

      // Finalize the archive
      archive.finalize();
    });
  }

  /**
   * Uploads a file to Google Cloud Storage
   * @param filePath Path to the file to upload
   * @returns Public URL of the uploaded file
   */
  async uploadToGCP(filePath: string): Promise<string> {
    // Check if GCP is configured
    if (!this.storage || !this.bucketName) {
      throw new Error('GCP is not configured. Cannot upload to GCP.');
    }

    try {
      // Check if the bucket exists, create it if it doesn't
      const [bucketExists] = await this.storage.bucket(this.bucketName).exists();
      if (!bucketExists) {
        this.logger.log(`Bucket ${this.bucketName} does not exist. Creating...`);
        await this.storage.createBucket(this.bucketName);
        this.logger.log(`Bucket ${this.bucketName} created.`);
      }

      const fileName = path.basename(filePath);
      const destination = `db-backups/${fileName}`;

      // Upload the file to the bucket
      await this.storage.bucket(this.bucketName).upload(filePath, {
        destination,
        metadata: {
          contentType: 'application/zip',
        },
      });

      // Get the public URL
      const publicUrl = `https://storage.googleapis.com/${this.bucketName}/${destination}`;

      // Clean up the local zip file
      await fsPromises.unlink(filePath);

      return publicUrl;
    } catch (error) {
      this.logger.error(`Failed to upload to GCP: ${error.message}`);
      throw new Error(`Failed to upload to GCP: ${error.message}`);
    }
  }
}