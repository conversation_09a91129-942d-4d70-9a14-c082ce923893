import { Model } from 'mongoose';
import { Client } from './schemas/client.schema';
export declare class RegisterClientService {
    private readonly clientModel;
    constructor(clientModel: Model<Client>);
    registerClient(applicationName: string, description?: string): Promise<Client>;
    updateClient(apiKey: string, updates: Partial<Client>): Promise<Client | null>;
    deactivateClient(apiKey: string): Promise<Client | null>;
}
