import { Schema, Document } from 'mongoose';

export interface Log extends Document {
  userId: string;
  action: string;
  resource: string;
  metadata: Record<string, any>; // Accepts dynamic metadata
  [key: string]: any; // Allows additional keys
}

export const LogSchema = new Schema<Log>(
  {
    userId: { type: String, required: true },
    action: { type: String, enum: ['CREATE', 'READ', 'UPDATE', 'DELETE'], required: true },
    resource: { type: String, required: true },
    metadata: { type: Schema.Types.Mixed }, // Accepts dynamic nested objects
  },
  {
    strict: false, // Allows additional fields at the root level
    timestamps: true, // Automatically adds createdAt and updatedAt fields
  }
);
