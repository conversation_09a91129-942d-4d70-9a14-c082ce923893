import { useState, useEffect } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '../ui/input';
import { Cross1Icon } from '@radix-ui/react-icons';

interface User {
  userId: string;
  userName: string;
}

interface UserFilterProps {
  users: User[];
  selectedUser: string;
  onUserChange: (value: string) => void;
}

export function UserFilter({
  users,
  selectedUser,
  onUserChange,
}: UserFilterProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState(searchTerm);

  const handleValueChange = (value: string) => {
    onUserChange(value);
    setIsOpen(false);
  };

  useEffect(() => {
    const timer = setTimeout(() => setDebouncedSearchTerm(searchTerm), 300);
    return () => clearTimeout(timer);
  }, [searchTerm]);

  const sortedUsers = [...users].sort((a, b) =>
    a.userName.localeCompare(b.userName)
  );

  const filteredUsers = sortedUsers.filter((user) =>
    user.userName.toLowerCase().includes(debouncedSearchTerm.toLowerCase())
  );

  const handleClearFilter = () => {
    onUserChange('all');
  };

  const handleSearchClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsOpen(true);
  };

  const handleDropdownToggle = (open: boolean) => {
    setIsOpen(open);
    if (!open) {
      setSearchTerm('');
      setDebouncedSearchTerm('');
    }
  };

  return (
    <div className="space-y-1">
      <div className="flex items-center">
        <h3 className="text-md font-medium">User Name</h3>
        {selectedUser !== 'all' && (
          <button
            className="p-1.5 ml-4 rounded-full bg-slate-50 hover:bg-gray-100"
            onClick={handleClearFilter}
          >
            <Cross1Icon className="h-4 w-4 text-gray-800" />
          </button>
        )}
      </div>
      <div className="flex items-center gap-2">
        <Select
          value={selectedUser}
          onValueChange={handleValueChange}
          open={isOpen}
          onOpenChange={handleDropdownToggle}
        >
          <SelectTrigger className="truncate max-w-[270px]">
            <SelectValue placeholder="Select User" />
          </SelectTrigger>
          <SelectContent>
            <div className="px-2 py-1 sticky top-0 bg-white z-10">
              <Input
                type="text"
                value={searchTerm}
                onChange={(e) => {
                  e.preventDefault();
                  setSearchTerm(e.target.value);
                }}
                placeholder="Search user..."
                className="mb-2"
                onClick={handleSearchClick}
                onKeyDown={(e) => e.stopPropagation()}
                autoComplete="off"
              />
            </div>
            <div className="max-h-[200px] overflow-y-auto">
              <SelectItem value="all">All Users</SelectItem>
              {filteredUsers.map((user) => (
                <SelectItem key={user.userId} value={user.userId}>
                  {user.userName} : {user.userType}
                </SelectItem>
              ))}
            </div>
          </SelectContent>
        </Select>
      </div>
    </div>
  );
}
