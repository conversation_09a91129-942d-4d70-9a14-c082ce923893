import { useState } from 'react';
import {
  She<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  SheetTitle,
} from '@/components/ui/sheet';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { AuditLogType } from '@/types/audit';
import { CopyIcon } from '@radix-ui/react-icons'; // Replace Lucide X with Radix Cross1Icon

interface AuditLogDetailsSheetProps {
  log: AuditLogType | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function AuditLogDetailsSheet({
  log,
  open,
  onOpenChange,
}: AuditLogDetailsSheetProps) {
  const [activeTab, setActiveTab] = useState('metadata');
  const [copySuccess, setCopySuccess] = useState(false);

  if (!log) return null;

 
  const handleCopy = () => {
    try {
      navigator.clipboard.writeText(JSON.stringify(log, null, 2));
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    } catch (err) {
      console.error("Failed to copy JSON data:", err);
    }
  };
  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent  className="w-full max-w-5xl sm:w-[720px] p-0">
        <SheetHeader className="p-6 pb-0">
          <SheetTitle className="text-xl font-semibold">Audit Log Details</SheetTitle>
        </SheetHeader>
        <Tabs
          value={activeTab}
          onValueChange={setActiveTab}
          className="w-full mt-4"
        >
          <TabsList className="w-full justify-start px-6">
            <TabsTrigger
              value="json"
              className="tab-trigger bg-slate-50"
            >
               Details
            </TabsTrigger>
            <TabsTrigger
              value="metadata"
              className="tab-trigger ml-4 bg-slate-50"
            >
              Metadata
            </TabsTrigger>
          </TabsList>
          <ScrollArea className="h-[calc(100vh-8rem)]">
            {/* <TabsContent value="basic" className="p-6 whitespace-wrap pt-4">
            <div className="space-y-6 overflow-auto whitespace-wrap">
                <div className="flex items-center justify-between whitespace-wrap">
                  <h4 className="text-sm font-medium text-gray-600">Raw Metadata</h4>
                  <button
                    onClick={handleCopyMetadata}
                    disabled={isCopying}
                    className={`px-3 py-1 text-sm font-medium text-white rounded-md shadow-sm focus:outline-none focus:ring focus:ring-blue-300 ${
                      isCopying
                        ? 'bg-blue-400 cursor-not-allowed'
                        : 'bg-blue-600 hover:bg-blue-700'
                    }`}
                  >
                    {isCopying ? 'Copying...' : 'Copy Metadata'}
                  </button>
                </div>
                <pre   style={{
    overflowX: 'auto',
    whiteSpace: 'pre',
    maxWidth: '100%',
  }} className="mt-2 text-sm w-full bg-gray-100 whitespace-wrap p-4 rounded-md overflow-x-auto text-gray-800 shadow-inner">
                  {JSON.stringify(log, null,2)}
                </pre>
              </div>
            </TabsContent> */}
               <TabsContent value="json" className="p-6 pt-4">
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  {/* <h4 className="text-sm font-medium text-gray-600">Raw Metadata</h4> */}
                  <button
            onClick={handleCopy}
            className="absolute top-2 right-6 bg-blue-600 text-white text-sm font-medium px-2 py-1 rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <CopyIcon className="inline h-4 w-4 mr-1" />
            {copySuccess ? "Copied!" : "Copy"}
          </button>
                </div>
                <pre className="mt-2 text-sm bg-gray-100 p-4 rounded-md overflow-x-scroll  text-gray-800 shadow-inner">
                  {JSON.stringify(log, null,2)}
                </pre>
              </div>
            </TabsContent>
            <TabsContent value="metadata" className="p-6 pt-4">
              <div className="space-y-6 ">
                <div className="flex items-center justify-between">
                  <h4 className="text-sm font-medium text-gray-600">Raw Metadata</h4>
                  <button
            onClick={handleCopy}
            className="absolute top-2 right-6 bg-blue-600 text-white text-sm font-medium px-2 py-1 rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <CopyIcon className="inline h-4 w-4 mr-1" />
            {copySuccess ? "Copied!" : "Copy"}
          </button>
                </div>
                <pre className="mt-2 text-sm bg-gray-100 p-4 rounded-md overflow-x-auto text-gray-800 shadow-inner">
  {JSON.stringify(log.metadata, null, 2)}
</pre>

              </div>
            </TabsContent>
          </ScrollArea>
        </Tabs>
      </SheetContent>
    </Sheet>
  );
}
