"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RegisterClientService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const client_schema_1 = require("./schemas/client.schema");
const uuid_1 = require("uuid");
let RegisterClientService = class RegisterClientService {
    constructor(clientModel) {
        this.clientModel = clientModel;
    }
    async registerClient(applicationName, description) {
        const apiKey = (0, uuid_1.v4)();
        const newClient = new this.clientModel({ apiKey, applicationName, description });
        return await newClient.save();
    }
    async updateClient(apiKey, updates) {
        return await this.clientModel.findOneAndUpdate({ apiKey }, updates, { new: true });
    }
    async deactivateClient(apiKey) {
        return await this.clientModel.findOneAndUpdate({ apiKey }, { status: 'INACTIVE' }, { new: true });
    }
};
exports.RegisterClientService = RegisterClientService;
exports.RegisterClientService = RegisterClientService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(client_schema_1.Client.name)),
    __metadata("design:paramtypes", [mongoose_2.Model])
], RegisterClientService);
//# sourceMappingURL=register-client.service.js.map