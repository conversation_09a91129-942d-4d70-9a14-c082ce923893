import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { RegisterClientController } from './register-client.controller';
import { RegisterClientService } from './register-client.service';
import { Client, ClientSchema } from './schemas/client.schema';

@Module({
  imports: [MongooseModule.forFeature([{ name: Client.name, schema: ClientSchema }])],
  controllers: [RegisterClientController],
  providers: [RegisterClientService],
})
export class RegisterClientModule {}
