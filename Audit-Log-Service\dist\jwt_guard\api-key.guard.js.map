{"version": 3, "file": "api-key.guard.js", "sourceRoot": "", "sources": ["../../src/jwt_guard/api-key.guard.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAAkG;AAClG,qCAAyC;AACzC,uCAAyC;AAGlC,IAAM,YAAY,GAAlB,MAAM,YAAY;IACvB,YACmB,UAAsB,EACtB,SAAoB;QADpB,eAAU,GAAV,UAAU,CAAY;QACtB,cAAS,GAAT,SAAS,CAAW;IACpC,CAAC;IAEJ,WAAW,CAAC,OAAyB;QAEnC,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAU,UAAU,EAAE,OAAO,CAAC,UAAU,EAAE,CAAC;YAC7D,IAAI,CAAC,SAAS,CAAC,GAAG,CAAU,UAAU,EAAE,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;QAE7E,IAAI,QAAQ,EAAE,CAAC;YACb,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,UAAU,EAAE,CAAC;QACpD,MAAM,UAAU,GAAG,OAAO,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;QAEpD,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;YACrD,MAAM,IAAI,8BAAqB,CAAC,yCAAyC,CAAC,CAAC;QAC7E,CAAC;QAED,MAAM,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAEvC,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,EAAE;gBAC5C,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,iBAAiB;aACtC,CAAC,CAAC;YACH,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC;YACvB,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,8BAAqB,CAAC,0BAA0B,CAAC,CAAC;QAC9D,CAAC;IACH,CAAC;CACF,CAAA;AAlCY,oCAAY;uBAAZ,YAAY;IADxB,IAAA,mBAAU,GAAE;qCAGoB,gBAAU;QACX,gBAAS;GAH5B,YAAY,CAkCxB"}