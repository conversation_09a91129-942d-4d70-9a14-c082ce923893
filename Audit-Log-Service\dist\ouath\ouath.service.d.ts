import { Model } from 'mongoose';
import { Authorization } from './schemas/authorization.schema';
export declare class AuthorizationService {
    private readonly authModel;
    constructor(authModel: Model<Authorization>);
    saveAuthCode(authCodeData: Partial<Authorization>): Promise<Authorization>;
    findAuthCode(authCode: string): Promise<Authorization | null>;
    expireAuthCode(authCode: string): Promise<void>;
}
