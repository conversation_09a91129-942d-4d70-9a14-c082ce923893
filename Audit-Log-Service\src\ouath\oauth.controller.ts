import { Controller, Get, Query, Post, Body, HttpStatus, Res, Req } from '@nestjs/common';
import { Response, Request } from 'express';
import { AuthorizationService } from './ouath.service';
import { JwtService } from '@nestjs/jwt';
import { LogsService } from '../modules/logs-module/logs.service';
import { SkipAuth } from '../jwt_guard/decorators/skipauth.decorators';
@Controller('oauth')
export class OauthController {
  constructor(
    private readonly authService: AuthorizationService,
    private readonly jwtService: JwtService,
    private readonly logsService: LogsService
  ) { }

  @SkipAuth()
  @Get('/authorize')
  async handleAuthorize(
    @Query() query: any,
    @Req() req: Request,
    @Res() res: Response,
  ) {
    const { state, school_id, redirect_uri } = query;

    const authCode = Math.random().toString(36).substring(2); // Simplified auth code generation
    const userIp = req.ip;

    await this.authService.saveAuthCode({
      authCode,
      school_id, // Replace this with actual user ID lookup
      state,
      ipAddress: userIp,
      isExpired: false
    });

    const redirectUrl = `${redirect_uri}?authorization_code=${authCode}&state=${state}&school_id=${school_id}`;
    return res.status(HttpStatus.OK).json({
      statusCode: HttpStatus.OK,
      message: 'Success',
      data: redirectUrl,
    });
  }

  @SkipAuth()
  @Get('/token')
  async handleToken(@Query() query: any, @Res() res: Response) {
    const { authorization_code, state,  school_id } = query;
    console.log("query", { authorization_code, state, school_id });
    const authCode = await this.authService.findAuthCode(authorization_code);
    console.log("authCode", authCode);
    if (!authCode || authCode.state !== state || authCode.isExpired) {
      return res.status(HttpStatus.BAD_REQUEST).json({
        statusCode: HttpStatus.BAD_REQUEST,
        message: 'Invalid or expired authorization code',
      });
    }

    await this.authService.expireAuthCode(authorization_code);
    // Create the payload for JWT tokens
    const payload = { school_id }; // Add any required fields
    // Generate access and refresh tokens
    const accessToken = this.jwtService.sign(payload, {
      secret: process.env.JWT_ACCESS_SECRET,
      expiresIn: '1d', // Access token expiration time
    });

    const refreshToken = this.jwtService.sign(payload, {
      secret: process.env.JWT_REFRESH_SECRET,
      expiresIn: '7d', // Refresh token expiration time
    });

    // const schoolFilter = await this.logsService.getSchoolFilter();
    // const actionFilter = await this.logsService.getActionFilter();
    // const resourceFilter = await this.logsService.getResourceFilter();

    return res.status(HttpStatus.OK).json({
      statusCode: HttpStatus.OK,
      message: 'User Logged In successfully',
      data: {
        accessToken,
        refreshToken,
        school: {
          school_id: school_id,
        },
       // schoolFilter, actionFilter, resourceFilter
      },
    });
  }
}
