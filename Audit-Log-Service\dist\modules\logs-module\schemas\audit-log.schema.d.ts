import { Schema as MongooseSchema, Document } from 'mongoose';
export declare class AuditLog extends Document {
    userId: string;
    action: string;
    resource: string;
    metadata: Record<string, any>;
}
export declare const AuditLogSchema: MongooseSchema<AuditLog, import("mongoose").Model<AuditLog, any, any, any, Document<unknown, any, AuditLog, any> & AuditLog & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, AuditLog, Document<unknown, {}, import("mongoose").FlatRecord<AuditLog>, {}> & import("mongoose").FlatRecord<AuditLog> & Required<{
    _id: unknown;
}> & {
    __v: number;
}>;
