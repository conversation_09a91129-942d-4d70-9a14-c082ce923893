import { OnModuleInit } from '@nestjs/common';
export declare class BackupService implements OnModuleInit {
    private readonly logger;
    private backupDir;
    private tempDir;
    private storage;
    private bucketName;
    private isBackupRunning;
    constructor();
    onModuleInit(): Promise<void>;
    private ensureBackupDirExists;
    private checkMongoDumpAvailability;
    scheduledBackup(): Promise<void>;
    cleanupOldBackups(): Promise<void>;
    private cleanupLocalBackups;
    private retryRemoveDirectory;
    private retryUnlink;
    private cleanupGCPBackups;
    private removeDirectory;
    createBackup(): Promise<string>;
    createZipFromBackup(backupPath: string): Promise<string>;
    uploadToGCP(filePath: string): Promise<string>;
}
