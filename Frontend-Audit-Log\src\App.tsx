import { useAuditLogs } from '@/hooks/useAuditLogs';
import { useAuditLogFilters } from '@/hooks/useAuditLogFilters';
import { AuditLogTable } from '@/components/audit/AuditLogTable';
import { DateRangeFilter } from '@/components/audit/DateRangeFilter';
import { ResourceFilter } from '@/components/audit/ResourceFilter';
import { ActionFilter } from '@/components/audit/ActionFilter';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Alert } from '@/components/ui/alert';
import { AlignCenterHorizontallyIcon } from '@radix-ui/react-icons';
import { SchoolFilter } from '@/components/audit/SchoolFilter';
import { getQueryParams, checkSessionStatus } from '@/services/api';
import { SessionExpiredModal } from '@/components/audit/SessionExpiredModal';
import { useEffect, useState } from 'react';
import { DeviceTypeFilter } from '@/components/audit/DeviceTypeFilter';
import { CustomLoader } from './components/audit/customLoader';
import { UserFilter } from './components/audit/UserFilter';

function App() {
    const [initialSessionCheck, setInitialSessionCheck] = useState({
        completed: false,
        valid: true
    });

    useEffect(() => {
        const checkSession = () => {
            const status = checkSessionStatus();
            setInitialSessionCheck({
                completed: true,
                valid: status.isValid
            });
        };

        setTimeout(checkSession, 100);
    }, []);

    const {
        logs,
        resources,
        actions,
        isLoading,
        hasCheckedAuth,
        error,
        sessionExpired,
        isAuthorizing,
        refreshLogs,
        schools,
        users,
        SelectedUser
    } = useAuditLogs();

    const {
        searchQuery,
        setSearchQuery,
        selectedResource,
        setSelectedResource,
        selectedAction,
        setSelectedAction,
        dateRange,
        selectedSchool,
        setSelectedSchool,
        setDateRange,
        selectedRange,
        handleRangeTypeChange,
        filteredLogs,
        selectedDeviceType,
        setSelectedDeviceType,
        selectedUser,
        setSelectedUser
    } = useAuditLogFilters(logs);

    const handleActionChange = (action: string) => {
        setSelectedAction(action);
    };

    const handleDeviceTypeChange = (deviceType: string) => {
        setSelectedDeviceType(deviceType);
    };

    const queryParams = getQueryParams();
    const { school_id, state } = queryParams;
    const isSchoolFilterVisible = school_id === '0';

    if (isAuthorizing || (isLoading && !hasCheckedAuth)) {
        return (
            <div className="flex h-screen items-center justify-center content-center">
                <CustomLoader />
            </div>
        );
    }

    if (initialSessionCheck.completed && !initialSessionCheck.valid) {
        return <SessionExpiredModal />;
    }

    return (
        <>
            <div className="flex h-screen bg-white overflow-y-hidden">
                <div className="w-full max-w-sm md:w-[19rem] bg-white p-4 border-r">
                    <div className="p-0 pb-0 flex justify-center items-center">
                        {state === "CT-RT" ? (
                            <img src="https://rt.clinicaltrac.net/upload/schools/1/logo/logo_small.png" className="w-[150px]" alt="CT-RT Logo" />
                        ) : state === "CT-RAD" ? (
                            <img src="https://rad.clinicaltrac.net/upload/schools/1/logo/logo_small.png" className="w-[150px]" alt="CT-RAD Logo" />
                        ) : null}
                    </div>

                    <ScrollArea className="h-full max-h-screen pt-4">
                        <div className="flex flex-col space-y-3">
                            <Separator />
                            {isSchoolFilterVisible && (
                                <SchoolFilter schools={schools} selectedSchool={selectedSchool} onSchoolChange={setSelectedSchool} />
                            )}
                            <DateRangeFilter dateRange={dateRange} onDateRangeChange={setDateRange} selectedRange={selectedRange} onRangeTypeChange={handleRangeTypeChange} />
                            <Separator />
                            <ResourceFilter resources={resources} selectedResource={selectedResource} onResourceChange={setSelectedResource} />
                            <Separator />
                            <ActionFilter actions={actions} selectedAction={selectedAction} onActionChange={handleActionChange} />
                            <Separator />
                            <DeviceTypeFilter selectedDeviceType={selectedDeviceType} onDeviceTypeChange={handleDeviceTypeChange} deviceTypes={[]} />
                             <Separator />
<UserFilter 
  users={users} 
  selectedUser={selectedUser} 
  onUserChange={setSelectedUser}
/>                             
                        </div>
                    </ScrollArea>
                </div>

                <div className="flex-1 flex flex-col bg-white min-w-0">
                    <div className="flex-1 p-2 pt-4 min-h-0">
                        {sessionExpired ? null : error ? (
                            <Alert variant="destructive">
                                <AlignCenterHorizontallyIcon className="h-4 w-4" />
                                {error}
                            </Alert>
                        ) : (
                            <AuditLogTable
                                logs={filteredLogs}
                                searchQuery={searchQuery}
                                onSearchChange={setSearchQuery}
                                selectedRange={selectedRange}
                                onRangeChange={handleRangeTypeChange}
                                selectedResource={selectedResource}
                                onClearResource={() => setSelectedResource('all')}
                                isLoading={isLoading}
                                selectedSchool={selectedSchool}
                                onRefresh={refreshLogs}
                                dateRange={dateRange}
                                selectedAction={selectedAction}
                                selectedDeviceType={selectedDeviceType}
                                schools={schools}
                                selectedUser={selectedUser}
                            />
                        )}
                    </div>
                </div>
            </div>
        </>
    );
}

export default App;
