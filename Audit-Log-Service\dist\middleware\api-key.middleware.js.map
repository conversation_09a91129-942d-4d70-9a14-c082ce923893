{"version": 3, "file": "api-key.middleware.js", "sourceRoot": "", "sources": ["../../src/middleware/api-key.middleware.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAA4D;AAE5D,qCAAyC;AAGlC,IAAM,aAAa,GAAnB,MAAM,aAAa;IACxB,YAA6B,UAAsB;QAAtB,eAAU,GAAV,UAAU,CAAY;IAAG,CAAC;IAEvD,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB;QACjD,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;QAChD,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;YACrD,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,cAAc,EAAE,CAAC,CAAC;QAC3D,CAAC;QAED,MAAM,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAEvC,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,EAAE;gBAC5C,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,iBAAiB;aACtC,CAAC,CAAC;YACH,GAAG,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC;YACtB,IAAI,EAAE,CAAC;QACT,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,0BAA0B,EAAE,CAAC,CAAC;QACvE,CAAC;IACH,CAAC;CACF,CAAA;AArBY,sCAAa;wBAAb,aAAa;IADzB,IAAA,mBAAU,GAAE;qCAE8B,gBAAU;GADxC,aAAa,CAqBzB"}