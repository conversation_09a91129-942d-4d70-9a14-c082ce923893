import { useState, useEffect } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Cross1Icon } from '@radix-ui/react-icons';
import React from 'react';

interface ResourceFilterProps {
  resources: string[];
  selectedResource: string;
  onResourceChange: (value: string) => void;
}

export function ResourceFilter({
  resources,
  selectedResource,
  onResourceChange,
}: ResourceFilterProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState(searchTerm);
  const [isOpen, setIsOpen] = useState(false);

  // Debouncing logic
  useEffect(() => {
    const timer = setTimeout(() => setDebouncedSearchTerm(searchTerm), 300);
    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Sort resources alphabetically before filtering
  const sortedResources = [...resources].sort((a, b) => a.localeCompare(b));

  // Filter resources based on search term
  const filteredResources = sortedResources.filter((resource) =>
    resource.toLowerCase().includes(debouncedSearchTerm.toLowerCase())
  );

  const handleSearchClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsOpen(true);
  };

  const handleValueChange = (value: string) => {
    onResourceChange(value);
    setIsOpen(false);
    setSearchTerm('');
    setDebouncedSearchTerm('');
  };

  const handleDropdownToggle = (open: boolean) => {
    setIsOpen(open);
    if (!open) {
      setSearchTerm('');
      setDebouncedSearchTerm('');
    }
  };

  const handleClear = () => {
    onResourceChange('all');
  };

  return (
    <div className="space-y-2">
      <h3 className="text-sm font-medium">Module</h3>
      <div className="flex items-center gap-2">
        <Select 
          value={selectedResource} 
          onValueChange={handleValueChange}
          open={isOpen}
          onOpenChange={handleDropdownToggle}
        >
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Select resource" />
          </SelectTrigger>
          <SelectContent>
            <div className="px-2 py-1 sticky top-0 bg-white z-10">
              <Input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search modules..."
                className="mb-2"
                onClick={handleSearchClick}
                onKeyDown={(e) => e.stopPropagation()}
                autoComplete="off"
              />
            </div>
            <div className="max-h-[200px] overflow-y-auto">
              <SelectItem value="all">All Modules</SelectItem>
              {filteredResources.map((resource) => (
                <SelectItem key={resource} value={resource}>
                  {resource}
                </SelectItem>
              ))}
            </div>
          </SelectContent>
        </Select>
        {selectedResource !== 'all' && (
          <button
            className="p-1.5 rounded-full bg-slate-50 hover:bg-gray-100"
            onClick={handleClear}
          >
            <Cross1Icon className="h-4 w-4 text-gray-500" />
          </button>
        )}
      </div>
    </div>
  );
}
