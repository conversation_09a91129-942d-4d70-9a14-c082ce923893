import { Response, Request } from 'express';
import { AuthorizationService } from './ouath.service';
import { JwtService } from '@nestjs/jwt';
import { LogsService } from '../modules/logs-module/logs.service';
export declare class OauthController {
    private readonly authService;
    private readonly jwtService;
    private readonly logsService;
    constructor(authService: AuthorizationService, jwtService: JwtService, logsService: LogsService);
    handleAuthorize(query: any, req: Request, res: Response): Promise<Response<any, Record<string, any>>>;
    handleToken(query: any, res: Response): Promise<Response<any, Record<string, any>>>;
}
